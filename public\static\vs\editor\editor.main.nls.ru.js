/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.20.0(6363745c0a33c27b149b89342a7b96d354fb554c)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.ru",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (произошло снова)","{0} (было {1} раз)"],"vs/base/browser/ui/findinput/findInput":["Ввод"],"vs/base/browser/ui/findinput/findInputCheckboxes":["С учетом регистра","Слово целиком","Использовать регулярное выражение"],"vs/base/browser/ui/findinput/replaceInput":["Ввод","Сохранить регистр"],"vs/base/browser/ui/inputbox/inputBox":["Ошибка: {0}","Предупреждение: {0}","Информация: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["свободный"],"vs/base/browser/ui/list/listWidget":["{0}. Для перехода используйте клавиши навигации."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Сброс","Отключить фильтр по типу","Включить фильтр по типу","Элементы не найдены","Сопоставлено элементов: {0} из {1}"],
"vs/base/common/keybindingLabels":["CTRL","SHIFT","ALT","Windows","CTRL","SHIFT","ALT","Превосходно","CTRL","SHIFT","ALT","Команда","CTRL","SHIFT","ALT","Windows","CTRL","SHIFT","ALT","Превосходно"],"vs/base/common/severity":["Ошибка","Предупреждение","Информация"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, средство выбора","средство выбора"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Средство быстрого выбора. Введите, чтобы сузить результаты.","Средство быстрого выбора","Результаты: {0}"],"vs/editor/browser/controller/coreCommands":["&&Выделить все","&&Отменить","&&Повторить"],"vs/editor/browser/controller/textAreaHandler":["Редактор сейчас недоступен. Чтобы открыть список действий, нажмите ALT+F1."],"vs/editor/browser/widget/codeEditorWidget":["Количество курсоров ограничено {0}."],"vs/editor/browser/widget/diffEditorWidget":["Нельзя сравнить файлы, потому что один из файлов слишком большой."],
"vs/editor/browser/widget/diffReview":["Закрыть","строки отсутствуют","1 строка","строк: {0}","Различие {0} из {1}; исходная версия: {2}, {3}, измененная версия: {4}, {5}","пустой","Исходная версия: {0}, измененная версия: {1}: {2}","+ измененная версия: {0}: {1}","- исходная версия: {0}: {1}","Перейти к следующему различию","Перейти к предыдущему различию"],"vs/editor/browser/widget/inlineDiffMargin":["Копировать удаленные строки","Копировать удаленную строку","Копировать удаленную строку ({0})","Отменить это изменение","Копировать удаленную строку ({0})"],
"vs/editor/common/config/commonEditorConfig":["Редактор",'Число пробелов в табуляции. Этот параметр переопределяется на основе содержимого файла, если установлен параметр "#editor.detectIndentation#".','Вставлять пробелы при нажатии клавиши TAB. Этот параметр переопределяется на основе содержимого файла, если установлен параметр "#editor.detectIndentation#". ','Управляет тем, будут ли параметры "#editor.tabSize#" и "#editor.insertSpaces#" определяться автоматически при открытии файла на основе содержимого файла.',"Удалить автоматически вставляемый конечный пробел.","Специальная обработка для больших файлов с отключением некоторых функций, которые интенсивно используют память.","Определяет, следует ли оценивать завершения на основе слов в документе.","Controls whether the semanticHighlighting is shown for the languages that support it.","Оставлять быстрый редактор открытым даже при двойном щелчке по его содержимому и при нажатии ESC.","Строки, длина которых превышает указанное значение, не будут размечены из соображений производительности","Время ожидания в миллисекундах, по истечении которого вычисление несовпадений отменяется. Укажите значение 0, чтобы не использовать время ожидания.","Определяет, как редактор несовпадений отображает отличия: рядом или в тексте.","Определяет, должен ли редактор несовпадений трактовать несовпадения символов-разделителей как различия.","Определяет, должны ли в редакторе отображаться индикаторы +/- для добавленных или удаленных изменений."],
"vs/editor/common/config/editorOptions":["Редактор будет определять, подключено ли средство чтения с экрана, с помощью API-интерфейсов платформы.","Редактор будет оптимизирован для использования со средством чтения с экрана в постоянном режиме.","Редактор никогда не будет оптимизироваться для использования со средством чтения с экрана.","Определяет, следует ли запустить редактор в режиме оптимизации для средства чтения с экрана.","Определяет, вставляется ли пробел при комментировании.","Управляет тем, копируется ли текущая строка при копировании без выделения.","Определяет, можно ли передать строку поиска в мини-приложение поиска из текста, выделенного в редакторе.",'Никогда не включать функцию "Найти в выделении" автоматически (по умолчанию)','Всегда включать функцию "Найти в выделении" автоматически','Автоматическое включение функции "Найти в выделении" при выборе нескольких строк содержимого.',"Определяет, выполняется ли поиск для выбранного текста или для всего файла в редакторе.","Определяет, должно ли мини-приложение поиска считывать или изменять общий буфер обмена поиска в macOS.","Определяет, должно ли мини-приложение поиска добавлять дополнительные строки в начале окна редактора. Если задано значение true, вы можете прокрутить первую строку при отображаемом мини-приложении поиска.","Включает/отключает лигатуры шрифта.","Явные параметры для функций шрифтов.","Настраивает лигатуры.","Управляет размером шрифта в пикселях.","Показать предварительные результаты (по умолчанию)","Перейти к основному результату и показать быстрый редактор","Перейдите к основному результату и включите быструю навигацию для остальных","Этот параметр устарел. Используйте вместо него отдельные параметры, например, 'editor.editor.gotoLocation.multipleDefinitions' или 'editor.editor.gotoLocation.multipleImplementations'.",'Управляет поведением команды "Перейти к определению" при наличии нескольких целевых расположений.','Управляет поведением команды "Перейти к определению типа" при наличии нескольких целевых расположений.','Управляет поведением команды "Перейти к объявлению" при наличии нескольких целевых расположений.','Управляет поведением команды "Перейти к реализациям" при наличии нескольких целевых расположений.','Управляет поведением команды "Перейти к ссылкам" при наличии нескольких целевых расположений.','Идентификатор альтернативной команды, выполняемой в том случае, когда результатом операции "Перейти к определению" является текущее расположение.','Идентификатор альтернативной команды, которая выполняется в том случае, если результатом операции "Перейти к определению типа" является текущее расположение.','Идентификатор альтернативный команды, выполняемой в том случае, когда результатом операции "Перейти к объявлению" является текущее расположение.','Идентификатор альтернативный команды, выполняемой, когда результатом команды "Перейти к реализации" является текущее расположение.','Идентификатор альтернативной команды, выполняемой в том случае, когда результатом выполнения операции "Перейти к ссылке" является текущее расположение.',"Управляет тем, отображается ли наведение.","Определяет время задержки в миллисекундах перед отображением наведения.","Управляет тем, должно ли наведение оставаться видимым при наведении на него курсора мыши.","Включает индикатор действия кода в редакторе.","Управляет высотой строк. Укажите 0 для вычисления высоты строки по размеру шрифта.","Определяет, отображается ли мини-карта.","Определяет, с какой стороны будет отображаться мини-карта.","Определяет, когда отображается ползунок мини-карты.","Масштаб содержимого, выводимого на мини-карте.","Отображает фактические символы в строке вместо цветных блоков.","Ограничивает ширину мини-карты, чтобы количество отображаемых столбцов не превышало определенное количество.","Включает всплывающее окно с документацией по параметру и сведениями о типе, которое отображается во время набора.","Определяет, меню подсказок остается открытым или закроется при достижении конца списка.","Разрешение кратких предложений в строках.","Разрешение кратких предложений в комментариях.","Разрешение кратких предложений вне строк и комментариев.","Определяет, должны ли при вводе текста автоматически отображаться предложения.","Номера строк не отображаются.","Отображаются абсолютные номера строк.","Отображаемые номера строк вычисляются как расстояние в строках до положения курсора.","Номера строк отображаются каждые 10 строк.","Управляет отображением номеров строк.","Отображать вертикальные линейки после определенного числа моноширинных символов. Для отображения нескольких линеек укажите несколько значений. Если не указано ни одного значения, вертикальные линейки отображаться не будут.","Вставить предложение без перезаписи текста справа от курсора.","Вставить предложение и перезаписать текст справа от курсора.","Определяет, будут ли перезаписываться слова при принятии вариантов завершения. Обратите внимание, что это зависит от расширений, использующих эту функцию.",'Определяет, следует ли выделять неожиданные изменения текста при принятии вариантов завершения, например, для замены необходим вариант "insertMode", но завершение поддерживает только вариант "insert".',"Управляет тем, допускаются ли небольшие опечатки в предложениях фильтрации и сортировки.","Определяет, следует ли учитывать при сортировке слова, расположенные рядом с курсором.",'Определяет, используются ли сохраненные варианты выбора предложений совместно несколькими рабочими областями и окнами (требуется "#editor.suggestSelection#").',"Управляет тем, могут ли использоваться быстрые предложения в активном фрагменте.","Указывает, нужно ли отображать значки в предложениях.","Определяет, сколько предложений IntelliSense будет показано до отображения полосы прокрутки (максимум 15).","Этот параметр устарел. Используйте вместо него отдельные параметры, например, 'editor.suggest.showKeywords' или 'editor.suggest.showSnippets'.",'Когда параметр включен, в IntelliSense отображаются предложения "method".','Когда параметр включен, в IntelliSense отображаются предложения "function".','Когда параметр включен, в IntelliSense отображаются предложения "constructor".','Когда параметр включен, в IntelliSense отображаются предложения "field".','Когда параметр включен, в IntelliSense отображаются предложения "variable".','Когда параметр включен, в IntelliSense отображаются предложения "class".','Когда параметр включен, в IntelliSense отображаются предложения "struct".','Когда параметр включен, в IntelliSense отображаются предложения "interface".','Когда параметр включен, в IntelliSense отображаются предложения "module".','Когда параметр включен, в IntelliSense отображаются предложения "property".','Когда параметр включен, в IntelliSense отображаются предложения "event".','Когда параметр включен, в IntelliSense отображаются предложения "operator".','Когда параметр включен, в IntelliSense отображаются предложения "unit".','Когда параметр включен, в IntelliSense отображаются предложения "value".','Когда параметр включен, в IntelliSense отображаются предложения "constant".','Когда параметр включен, в IntelliSense отображаются предложения "enum".','Когда параметр включен, в IntelliSense отображаются предложения "enumMember".','Когда параметр включен, в IntelliSense отображаются предложения "keyword".','Когда параметр включен, в IntelliSense отображаются предложения "text".','Когда параметр включен, в IntelliSense отображаются предложения "color".','Когда параметр включен, в IntelliSense отображаются предложения "file".','Когда параметр включен, в IntelliSense отображаются предложения "reference".','Когда параметр включен, в IntelliSense отображаются предложения "customcolor".','Когда параметр включен, в IntelliSense отображаются предложения "folder".','Когда параметр включен, в IntelliSense отображаются предложения "typeParameter".','Когда параметр включен, в IntelliSense отображаются предложения "snippet".',"Определяет видимость строки состояния в нижней части виджета предложений.",'Определяет, будут ли предложения приниматься при вводе символов фиксации. Например, в JavaScript точка с запятой (";") может быть символом фиксации, при вводе которого предложение принимается.',"Принимать предложение при нажатии клавиши ВВОД только в том случае, если оно изменяет текст.","Определяет, будут ли предложения приниматься клавишей ВВОД в дополнение к клавише TAB. Это помогает избежать неоднозначности между вставкой новых строк и принятием предложений.","Задает количество строк в редакторе, которые могут быть прочитаны средством чтения с экрана. Предупреждение: из-за технических ограничений этот число не может превышать значение по умолчанию.","Содержимое редактора","Использовать конфигурации языка для автоматического закрытия скобок.","Автоматически закрывать скобки только в том случае, если курсор находится слева от пробела.","Определяет, должен ли редактор автоматически добавлять закрывающую скобку при вводе пользователем открывающей скобки.","Заменять закрывающие кавычки и скобки при вводе только в том случае, если кавычки или скобки были вставлены автоматически.","Определяет, должны ли в редакторе заменяться закрывающие кавычки или скобки при вводе.","Использовать конфигурации языка для автоматического закрытия кавычек.","Автоматически закрывать кавычки только в том случае, если курсор находится слева от пробела.","Определяет, должен ли редактор автоматически закрывать кавычки, если пользователь добавил открывающую кавычку.","Редактор не будет вставлять отступы автоматически.","Редактор будет сохранять отступ текущей строки.","Редактор будет сохранять отступы текущей строки и учитывать скобки в соответствии с синтаксисом языка.","Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки и вызывать специальные правила onEnterRules, определяемые языками.","Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки, вызывать специальные правила onEnterRules, определяемые языками и учитывать правила отступа indentationRules, определяемые языками.","Определяет, должен ли редактор автоматически изменять отступы, когда пользователи вводят, вставляют или перемещают текст или изменяют отступы строк.","Использовать конфигурации языка для автоматического обрамления выделений.","Обрамлять с помощью кавычек, а не скобок.","Обрамлять с помощью скобок, а не кавычек.","Определяет, должен ли редактор автоматически обрамлять выделения.","Определяет, отображается ли CodeLens в редакторе.","Определяет, должны ли в редакторе отображаться внутренние декораторы цвета и средство выбора цвета.","Определяет, будет ли текст скопирован в буфер обмена с подсветкой синтаксиса.","Управляет стилем анимации курсора.","Управляет тем, следует ли включить плавную анимацию курсора.","Управляет стилем курсора.",'Определяет минимальное число видимых начальных и конечных линий, окружающих курсор. Этот параметр имеет название "scrollOff" или "scrollOffset" в некоторых других редакторах.','"cursorSurroundingLines" применяется только при запуске с помощью клавиатуры или API.','"cursorSurroundingLines" принудительно применяется во всех случаях.','Определяет, когда необходимо применять "cursorSurroundingLines".',"Управляет шириной курсора, когда для параметра \"#editor.cursorStyle#\" установлено значение 'line'","Определяет, следует ли редактору разрешить перемещение выделенных элементов с помощью перетаскивания.","Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.","Определяет, включено ли свертывание кода в редакторе.","Управляет тем, как вычисляются диапазоны сворачивания. При указании параметра 'auto' используется стратегия сворачивания на основе языка, если она доступна. При указании параметра 'indentation' принудительно используется стратегия на основе отступов.","Определяет, должен ли редактор выделять сложенные диапазоны.","Определяет семейство шрифтов.","Управляет насыщенностью шрифта.","Определяет, будет ли редактор автоматически форматировать вставленное содержимое. Модуль форматирования должен быть доступен и иметь возможность форматировать диапазон в документе.","Управляет параметром, определяющим, должен ли редактор автоматически форматировать строку после ввода.","Управляет отображением вертикальных полей глифа в редакторе. Поля глифа в основном используются для отладки.","Управляет скрытием курсора в обзорной линейке.","Управляет тем, должна ли выделяться активная направляющая отступа в редакторе.","Управляет интервалом между буквами в пикселях.","Определяет, должен ли редактор определять ссылки и делать их доступными для щелчка.","Выделять соответствующие скобки.","Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.","Изменение размера шрифта в редакторе при нажатой клавише CTRL и движении колесика мыши.","Объединить несколько курсоров, когда они перекрываются.","Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.","Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.",'Модификатор, который будет использоваться для добавления нескольких курсоров с помощью мыши. Жесты мыши "Перейти к определению" и "Открыть ссылку" будут изменены так, чтобы они не конфликтовали с несколькими курсорами. [Дополнительные сведения](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier). ',"Каждый курсор вставляет одну строку текста.","Каждый курсор вставляет полный текст.","Управляет вставкой, когда число вставляемых строк соответствует числу курсоров.","Определяет, должен ли редактор выделять экземпляры семантических символов.","Определяет, должна ли отображаться граница на обзорной линейке.","Фокусировка на дереве при открытии обзора","Фокусировка на редакторе при открытии обзора","Определяет, следует ли переключить фокус на встроенный редактор или дерево в виджете обзора.","Управляет длительностью задержки (в мс) перед отображением кратких предложений.","Определяет, должны ли в редакторе отображаться управляющие символы.","Определяет, должны ли в редакторе отображаться направляющие отступа.","Отображение номера последней строки, когда файл заканчивается новой строкой.","Выделяет поле и текущую строку.","Определяет, должен ли редактор выделять текущую строку.","Render whitespace characters except for single spaces between words.","Отображать пробелы только в выделенном тексте.","Определяет, должны ли в редакторе отображаться пробелы.","Управляет тем, необходимо ли отображать скругленные углы для выделения.","Управляет количеством дополнительных символов, на которое содержимое редактора будет прокручиваться по горизонтали.","Определяет, будет ли содержимое редактора прокручиваться за последнюю строку.","Контролирует, следует ли поддерживать первичный буфер обмена Linux.","Определяет, должен ли редактор выделять совпадения, аналогичные выбранному фрагменту.","Определяет, будут ли автоматически скрываться элементы управления свертыванием на полях.","Управляет скрытием неиспользуемого кода.","Отображать предложения фрагментов поверх других предложений.","Отображать предложения фрагментов под другими предложениями.","Отображать предложения фрагментов рядом с другими предложениями.","Не отображать предложения фрагментов.","Управляет отображением фрагментов вместе с другими предложениями и их сортировкой.","Определяет, будет ли использоваться анимация при прокрутке содержимого редактора",'Размер шрифта мини-приложения с предложениями. Если установить значение "0", будет использовано значение "#editor.fontSize#".','Высота строки мини-приложения с предложениями. Если установить значение "0", будет использовано значение "#editor.lineHeight#".',"Определяет, должны ли при вводе триггерных символов автоматически отображаться предложения.","Всегда выбирать первое предложение.",'Выбор недавних предложений, если только дальнейший ввод не приводит к использованию одного из них, например "console.| -> console.log", так как "log" недавно использовался для завершения.','Выбор предложений с учетом предыдущих префиксов, использованных для завершения этих предложений, например "co -> console" и "con -> const".',"Управляет предварительным выбором предложений при отображении списка предложений.","При использовании дополнения по TAB будет добавляться наилучшее предложение при нажатии клавиши TAB.","Отключить дополнение по TAB.",'Вставка дополнений по TAB при совпадении их префиксов. Функция работает оптимально, если параметр "quickSuggestions" отключен.',"Включает дополнения по TAB.","Вставка и удаление пробелов после позиции табуляции","Символы, которые будут использоваться как разделители слов при выполнении навигации или других операций, связанных со словами.","Строки не будут переноситься никогда.","Строки будут переноситься по ширине окна просмотра.",'Строки будут переноситься по "#editor.wordWrapColumn#".','Строки будут перенесены по минимальному значению из двух: ширина окна просмотра и "#editor.wordWrapColumn#".',"Управляет тем, как следует переносить строки.",'Определяет столбец переноса редактора, если значение "#editor.wordWrap#" — "wordWrapColumn" или "bounded".',"Без отступа. Перенос строк начинается со столбца 1.","Перенесенные строки получат тот же отступ, что и родительская строка.","Перенесенные строки получат отступ, увеличенный на единицу по сравнению с родительской строкой. ","Перенесенные строки получат отступ, увеличенный на два по сравнению с родительской строкой.","Управляет отступом строк с переносом по словам.","Предполагает, что все символы имеют одинаковую ширину. Это быстрый алгоритм, который работает правильно для моноширинных шрифтов и некоторых скриптов (например, латинских символов), где глифы имеют одинаковую ширину.","Делегирует вычисление точек переноса браузеру. Это медленный алгоритм, который может привести к зависаниям при обработке больших файлов, но работает правильно во всех случаях.","Управляет алгоритмом, вычисляющим точки переноса."],
"vs/editor/common/modes/modesRegistry":["Простой текст"],
"vs/editor/common/standaloneStrings":["Ничего не выбрано","Строка {0}, столбец {1} (выбрано: {2})","Строка {0}, столбец {1}","Выделений: {0} (выделено символов: {1})","Выделений: {0}",'Теперь для параметра "accessibilitySupport" устанавливается значение "вкл".',"Открывается страница документации о специальных возможностях редактора.","в панели только для чтения редактора несовпадений.","на панели редактора несовпадений."," в редакторе кода только для чтения"," в редакторе кода","Чтобы оптимизировать редактор для использования со средством чтения с экрана, нажмите COMMAND+E.","Чтобы оптимизировать редактор для использования со средством чтения с экрана, нажмите CTRL+E.","Редактор настроен для оптимальной работы со средством чтения с экрана.","Редактор настроен без оптимизации для использования средства чтения с экрана, что не подходит в данной ситуации.","При нажатии клавиши TAB в текущем редакторе фокус ввода переместится на следующий элемент, способный его принять. Чтобы изменить это поведение, нажмите клавишу {0}.","При нажатии клавиши TAB в текущем редакторе фокус ввода переместится на следующий элемент, способный его принять. Команду {0} сейчас невозможно выполнить с помощью настраиваемого сочетания клавиш.","При нажатии клавиши TAB в текущем редакторе будет вставлен символ табуляции. Чтобы изменить это поведение, нажмите клавишу {0}.","При нажатии клавиши TAB в текущем редакторе будет вставлен символ табуляции. Команду {0} сейчас невозможно выполнить с помощью настраиваемого сочетания клавиш.","Нажмите COMMAND+H, чтобы открыть окно браузера с дополнительной информацией о специальных возможностях редактора.","Нажмите CTRL+H, чтобы открыть окно браузера с дополнительной информацией о специальных возможностях редактора.","Вы можете закрыть эту подсказку и вернуться в редактор, нажав клавиши ESCAPE или SHIFT+ESCAPE.","Показать справку по специальным возможностям","Разработчик: проверить токены","Перейти к строке {0} и символу {1}","Перейти к строке {0}","Введите номер строки от 1 до {0} для перехода","Введите символ между 1 и {0} для перехода к","Текущая строка: {0}. Перейти к строке {1}.","Введите номер строки с последующим необязательным двоеточием и номер символа, к которому нужно перейти","Перейти к строке...","{0}, {1}, команды","{0}, команды","Введите имя действия, которое нужно выполнить","Палитра команд","{0}, символы","Введите имя идентификатора, к которому вы хотите перейти","Перейти к символу...","символы ({0})","модули ({0})","классы ({0})","интерфейсы ({0})","методы ({0})","функции ({0})","свойства ({0})","переменные ({0})","переменные ({0})","конструкторы ({0})","вызовы ({0})","Содержимое редактора","Нажмите клавиши CTRL+F1 для доступа к параметрам специальных возможностей.","Нажмите ALT+F1 для доступа к параметрам специальных возможностей.","Переключить высококонтрастную тему","Внесено изменений в файлах ({1}): {0}."],
"vs/editor/common/view/editorColorRegistry":["Цвет фона для выделения строки в позиции курсора.","Цвет фона границ вокруг строки в позиции курсора.","Цвет фона для выделенных диапазонов, например при использовании функций Quick Open или поиска. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет фона обводки выделения.",'Цвет фона выделенного символа, например, в функциях "Перейти к определению" или "Перейти к следующему/предыдущему символу". Цвет должен быть прозрачным, чтобы не скрывать оформление текста под ним.',"Цвет фона для границы вокруг выделенных символов.","Цвет курсора редактора.","Цвет фона курсора редактора. Позволяет настраивать цвет символа, перекрываемого прямоугольным курсором.","Цвет пробелов в редакторе.","Цвет направляющих для отступов редактора.","Цвет активных направляющих для отступов редактора.","Цвет номеров строк редактора.","Цвет номера активной строки редактора","Параметр 'Id' является устаревшим. Используйте вместо него параметр 'editorLineNumber.activeForeground'.","Цвет номера активной строки редактора","Цвет линейки редактора.","Цвет переднего плана элемента CodeLens в редакторе","Цвет фона парных скобок","Цвет прямоугольников парных скобок","Цвет границы для линейки в окне просмотра.","Цвет фона поля в редакторе. В поле размещаются отступы глифов и номера строк.","Цвет границы для ненужного (неиспользуемого) исходного кода в редакторе.",'Непрозрачность ненужного (неиспользуемого) исходного кода в редакторе. Например, "#000000c0" отображает код с непрозрачностью 75 %. В высококонтрастных темах для выделения ненужного кода вместо затенения используйте цвет темы "editorUnnecessaryCode.border".',"Цвет метки линейки в окне просмотра для ошибок.","Цвет метки линейки в окне просмотра для предупреждений.","Цвет метки линейки в окне просмотра для информационных сообщений."],
"vs/editor/contrib/bracketMatching/bracketMatching":["Цвет метки линейки в окне просмотра для пар скобок.","Перейти к скобке","Выбрать скобку","Перейти к &&скобке"],"vs/editor/contrib/caretOperations/caretOperations":["Переместить курсор влево","Переместить курсор вправо"],"vs/editor/contrib/caretOperations/transpose":["Транспортировать буквы"],"vs/editor/contrib/clipboard/clipboard":["Вырезать","&&Вырезать","Копирование","&&Копировать","Вставить","&&Вставить","Копировать с выделением синтаксиса"],
"vs/editor/contrib/codeAction/codeActionCommands":["Тип запускаемого действия кода.","Определяет, когда применяются возвращенные действия.","Всегда применять первое возвращенное действие кода.","Применить первое действие возвращенного кода, если оно является единственным.","Не применять действия возвращенного кода.","Определяет, следует ли возвращать только предпочтительные действия кода.","При применении действия кода произошла неизвестная ошибка","Быстрое исправление...","Доступные действия кода отсутствуют",'Нет доступных предпочтительных действий кода для "{0}".','Действия кода для "{0}" недоступны',"Нет доступных предпочтительных действий кода","Доступные действия кода отсутствуют","Рефакторинг...",'Нет доступных предпочтительных рефакторингов для "{0}"','Нет доступного рефакторинга для "{0}"',"Нет доступных предпочтительных рефакторингов","Доступные операции рефакторинга отсутствуют","Действие с исходным кодом...","Нет доступных предпочтительных действий источника для '{0}'",'Нет доступных исходных действий для "{0}"',"Предпочтительные действия источника недоступны","Доступные исходные действия отсутствуют","Организация импортов","Действие для упорядочения импортов отсутствует","Исправить все","Нет доступного действия по общему исправлению","Автоисправление...","Нет доступных автоисправлений"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Отображение исправлений. Доступно предпочитаемое исправление ({0})","Показать исправления ({0})","Показать исправления"],"vs/editor/contrib/comment/comment":["Закомментировать или раскомментировать строку","Переключить комментарий &&строки","Закомментировать строку","Раскомментировать строку","Закомментировать или раскомментировать блок","Переключить комментарий &&блока"],"vs/editor/contrib/contextmenu/contextmenu":["Показать контекстное меню редактора"],"vs/editor/contrib/cursorUndo/cursorUndo":["Отмена действия курсора","Повтор действия курсора"],
"vs/editor/contrib/documentSymbols/outlineTree":["Цвет переднего плана для символов массива. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для логических символов. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов класса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов цвета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов константы. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов конструктора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов члена перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов события. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов поля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов файла. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов папки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов функции. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов интерфейса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов ключа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов ключевого слова. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов метода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов модуля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов пространства имен. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов NULL. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов числа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов объекта. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов оператора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов пакета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов свойства. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов ссылки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов фрагмента кода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов строки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов структуры. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов текста. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов типа параметров. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов единиц. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.","Цвет переднего плана для символов переменной. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений."],
"vs/editor/contrib/find/findController":["Найти","&&Найти","Найти в выбранном","Найти далее","Найти далее","Найти ранее","Найти ранее","Найти следующее выделение","Найти предыдущее выделение","Заменить","&&Заменить"],"vs/editor/contrib/find/findWidget":["Найти","Найти","Предыдущее соответствие","Следующее соответствие","Найти в выделении","Закрыть","Заменить","Заменить","Заменить","Заменить все",'Режим "Переключение замены"',"Отображаются только первые {0} результатов, но все операции поиска выполняются со всем текстом.","{0} из {1}","Результаты отсутствуют","{0} обнаружено","{0} обнаружено для {1}","{0} обнаружено для {1} в {2}","{0} обнаружено для {1}","Теперь при нажатии клавиш CTRL+ВВОД вставляется символ перехода на новую строку вместо замены всего текста. Вы можете изменить сочетание клавиш editor.action.replaceAll, чтобы переопределить это поведение."],
"vs/editor/contrib/folding/folding":["Развернуть","Развернуть рекурсивно","Свернуть","Переключить свертывание","Свернуть рекурсивно","Свернуть все блоки комментариев","Свернуть все регионы","Развернуть все регионы","Свернуть все","Развернуть все","Уровень папки {0}","Цвет выделения редактора."],"vs/editor/contrib/fontZoom/fontZoom":["Увеличить шрифт редактора","Уменьшить шрифт редактора","Сбросить масштаб шрифта редактора"],"vs/editor/contrib/format/format":["Внесена одна правка форматирования в строке {0}.","Внесены правки форматирования ({0}) в строке {1}.","Внесена одна правка форматирования между строками {0} и {1}.","Внесены правки форматирования ({0}) между строками {1} и {2}."],"vs/editor/contrib/format/formatActions":["Форматировать документ","Форматировать выделенный фрагмент"],
"vs/editor/contrib/gotoError/gotoError":["Перейти к Следующей Проблеме (Ошибке, Предупреждению, Информации)","Перейти к Предыдущей Проблеме (Ошибке, Предупреждению, Информации)","Перейти к следующей проблеме в файлах (ошибки, предупреждения, информационные сообщения)","Перейти к предыдущей проблеме в файлах (ошибки, предупреждения, информационные сообщения)","Следующая &&проблема","Предыдущая &&проблема"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Проблемы: {0} из {1}","Проблемы: {0} из {1}","Цвет ошибки в мини-приложении навигации по меткам редактора.","Цвет предупреждения в мини-приложении навигации по меткам редактора.","Цвет информационного сообщения в мини-приложении навигации по меткам редактора.","Фон мини-приложения навигации по меткам редактора."],
"vs/editor/contrib/gotoSymbol/goToCommands":["Обзор","Определения",'Определение для "{0}" не найдено.',"Определения не найдены.","Перейти к определению","Перейти к &&определению","Открыть определение сбоку","Показать определение","Объявления",'Объявление для "{0}" не найдено.',"Объявление не найдено","Перейти к объявлению","Перейти к &&объявлению",'Объявление для "{0}" не найдено.',"Объявление не найдено","Просмотреть объявление","Определения типов",'Не найдено определение типа для "{0}".',"Не найдено определение типа.","Перейти к определению типа","Перейти к &&определению типа","Показать определение типа","Реализации",'Не найдена реализация для "{0}".',"Не найдена реализация.","Перейти к реализациям","Перейти к &&реализациям","Просмотреть реализации",'Ссылки для "{0}" не найдены',"Ссылки не найдены","Перейти к ссылкам","Перейти к &&ссылкам","Ссылки","Показать ссылки","Ссылки","Перейти к любому символу","Расположения",'Результаты для "{0}" отсутствуют',"Ссылки"],
"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Щелкните, чтобы отобразить определения ({0})."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Загрузка...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["Не удалось разрешить файл.","Ссылок: {0}","{0} ссылка"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["предварительный просмотр недоступен","Ссылки","Результаты отсутствуют","Ссылки"],"vs/editor/contrib/gotoSymbol/referencesModel":["ссылка в {0} в строке {1} и символе {2}","1 символ в {0}, полный путь: {1}","{0} символов в {1}, полный путь: {2} ","Результаты не найдены","Обнаружен 1 символ в {0}","Обнаружено {0} символов в {1}","Обнаружено {0} символов в {1} файлах"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Символ {0} из {1}, {2} для следующего","Символ {0} из {1}"],"vs/editor/contrib/hover/hover":["Показать при наведении","Отображать предварительный просмотр определения при наведении курсора мыши"],
"vs/editor/contrib/hover/modesContentHover":["Загрузка...","Проблема при обзоре","Проверка наличия исправлений...","Исправления недоступны","Быстрое исправление..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Заменить предыдущим значением","Заменить следующим значением"],"vs/editor/contrib/linesOperations/linesOperations":["Копировать строку сверху","&&Копировать на строку выше","Копировать строку снизу","Копировать на строку &&ниже","Дублировать выбранное","&&Дублировать выбранное","Переместить строку вверх","Переместить на с&&троку выше","Переместить строку вниз","&&Переместить на строку ниже","Сортировка строк по возрастанию","Сортировка строк по убыванию","Удалить конечные символы-разделители","Удалить строку","Увеличить отступ","Уменьшить отступ","Вставить строку выше","Вставить строку ниже","Удалить все слева","Удалить все справа","_Объединить строки","Транспонировать символы вокруг курсора","Преобразовать в верхний регистр","Преобразовать в нижний регистр","Преобразовать в заглавные буквы"],
"vs/editor/contrib/links/links":["Выполнить команду","перейти по ссылке","Кнопка OPTION и щелчок левой кнопкой мыши","Кнопка CTRL и щелчок левой кнопкой мыши","Кнопка OPTION и щелчок левой кнопкой мыши","Кнопка ALT и щелчок левой кнопкой мыши","Не удалось открыть ссылку, так как она имеет неправильный формат: {0}","Не удалось открыть ссылку, у нее отсутствует целевой объект.","Открыть ссылку"],"vs/editor/contrib/message/messageController":["Не удается выполнить изменение в редакторе только для чтения"],
"vs/editor/contrib/multicursor/multicursor":["Добавить курсор выше","Добавить курсор &&выше","Добавить курсор ниже","Добавить курсор &&ниже","Добавить курсоры к окончаниям строк","Добавить курсоры в &&окончания строк","Добавить курсоры ниже","Добавить курсоры выше","Добавить выделение в следующее найденное совпадение","Добавить &&следующее вхождение","Добавить выделенный фрагмент в предыдущее найденное совпадение","Добавить &&предыдущее вхождение","Переместить последнее выделение в следующее найденное совпадение","Переместить последний выделенный фрагмент в предыдущее найденное совпадение","Выбрать все вхождения найденных совпадений","Выбрать все &&вхождения","Изменить все вхождения"],"vs/editor/contrib/parameterHints/parameterHints":["Переключить подсказки к параметрам"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, подсказка"],
"vs/editor/contrib/peekView/peekView":["Закрыть","Цвет фона области заголовка быстрого редактора.","Цвет заголовка быстрого редактора.","Цвет сведений о заголовке быстрого редактора.","Цвет границ быстрого редактора и массива.","Цвет фона в списке результатов представления быстрого редактора.","Цвет переднего плана узлов строки в списке результатов быстрого редактора.","Цвет переднего плана узлов файла в списке результатов быстрого редактора.","Цвет фона выбранной записи в списке результатов быстрого редактора.","Цвет переднего плана выбранной записи в списке результатов быстрого редактора.","Цвет фона быстрого редактора.","Цвет фона поля в окне быстрого редактора.","Цвет выделения совпадений в списке результатов быстрого редактора.","Цвет выделения совпадений в быстром редакторе.","Граница выделения совпадений в быстром редакторе."],
"vs/editor/contrib/rename/rename":["Результаты отсутствуют.","Произошла неизвестная ошибка при определении расположения после переименования",'Идет переименование "{0}"',"«{0}» успешно переименован в «{1}». Сводка: {2}","Операции переименования не удалось применить правки","Операции переименования не удалось вычислить правки","Переименовать символ","Включить/отключить возможность предварительного просмотра изменений перед переименованием"],"vs/editor/contrib/rename/renameInputField":["Введите новое имя для входных данных и нажмите клавишу ВВОД для подтверждения.","{0} для переименования, {1} для предварительного просмотра"],"vs/editor/contrib/smartSelect/smartSelect":["Развернуть выбранный фрагмент","&&Развернуть выделение","Уменьшить выделенный фрагмент","&&Сжать выделение"],
"vs/editor/contrib/snippet/snippetVariables":["воскресенье","понедельник","вторник","среда","четверг","пятница","суббота","Вс","Пн","Вт","Ср","Чт","Пт","Сб","Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь","Янв","Фев","Мар","Апр","Май","Июн","Июл","Авг","Сен","Окт","Ноя","Дек"],"vs/editor/contrib/suggest/suggestController":['Принятие "{0}" привело к внесению дополнительных правок ({1})',"Переключить предложение"],
"vs/editor/contrib/suggest/suggestWidget":["Цвет фона виджета подсказок.","Цвет границ виджета подсказок.","Цвет переднего плана мини-приложения предложений.","Фоновый цвет выбранной записи в мини-приложении предложений.","Цвет выделения соответствия в мини-приложении предложений.","Подробнее...{0}","Кратко...{0}","Загрузка...","Загрузка...","Предложения отсутствуют.","{0} для просмотра сокращенных сведений...","{0} для получения дополнительных сведений...","Элемент {0}, документы: {1}","{0} для вставки, {1} для замены","{0} для замены, {1} для вставки","{0} для принятия"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Переключение клавиши TAB перемещает фокус.","При нажатии клавиши TAB фокус перейдет на следующий элемент, который может получить фокус","Теперь при нажатии клавиши TAB будет вставлен символ табуляции"],"vs/editor/contrib/tokenization/tokenization":["Разработчик: принудительная повторная установка токенов"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Цвет фона символа при доступе на чтение, например, при чтении переменной. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет фона для символа во время доступа на запись, например при записи в переменную. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет границы символа при доступе на чтение, например, при считывании переменной.","Цвет границы символа при доступе на запись, например, при записи переменной. ","Цвет маркера обзорной линейки для выделения символов. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.","Цвет маркера обзорной линейки для выделения символов доступа на запись. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Перейти к следующему выделению символов","Перейти к предыдущему выделению символов","Включить или отключить выделение символов"],
"vs/platform/configuration/common/configurationRegistry":["Переопределения конфигурации по умолчанию","Настройка параметров редактора, переопределяемых для языка.","Этот параметр не поддерживает настройку для отдельных языков.","Невозможно зарегистрировать \"{0}\". Оно соответствует шаблону свойства '\\\\[.*\\\\]$' для описания параметров редактора, определяемых языком. Используйте участие configurationDefaults.",'Невозможно зарегистрировать "{0}". Это свойство уже зарегистрировано.'],"vs/platform/keybinding/common/abstractKeybindingService":["Была нажата клавиша ({0}). Ожидание нажатия второй клавиши сочетания...","Сочетание клавиш ({0} и {1}) не является командой."],
"vs/platform/list/browser/listService":["Рабочее место","Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.","Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.",'Модификатор, который будет использоваться для добавления элементов в деревьях и списках в элемент множественного выбора с помощью мыши (например, в проводнике, в открытых редакторах и в представлении scm). Жесты мыши  "Открыть сбоку" (если они поддерживаются) будут изменены таким образом, чтобы они не конфликтовали с модификатором элемента множественного выбора.',"Управляет тем, как открывать элементы в деревьях и списках с помощью мыши (если поддерживается). Для родительских элементов с дочерними элементами в деревьях этот параметр управляет тем, будет ли родительский элемент разворачиваться по одинарному или по двойному щелчку мыши. Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним. ","Определяет, поддерживают ли списки и деревья горизонтальную прокрутку в Workbench.","Определяет, поддерживают ли древа горизонтальную прокрутку в рабочей области.",'Этот параметр объявлен нерекомендуемым, используйте вместо него "{0}".',"Определяет отступ для дерева в пикселях.","Определяет, нужно ли в дереве отображать направляющие отступа.","Про простой навигации с клавиатуры выбираются элементы, соответствующие вводимым с клавиатуры данным. Сопоставление осуществляется только по префиксам.","Функция подсветки навигации с клавиатуры выделяет элементы, соответствующие вводимым с клавиатуры данным. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.","Фильтр навигации с клавиатуры позволяет отфильтровать и скрыть все элементы, не соответствующие вводимым с клавиатуры данным.","Управляет стилем навигации с клавиатуры для списков и деревьев в Workbench. Доступен простой режим, режим выделения и режим фильтрации.",'Указывает, активируется ли навигация с помощью клавиатуры в списках и деревьях автоматически простым вводом. Если задано значение "false", навигация с клавиатуры активируется только при выполнении команды "list.toggleKeyboardNavigation", для которой можно назначить сочетание клавиш.'],
"vs/platform/markers/common/markers":["Ошибка","Предупреждение","Информация"],
"vs/platform/theme/common/colorRegistry":["Общий цвет переднего плана. Этот цвет используется, только если его не переопределит компонент.","Общий цвет переднего плана для сообщений об ошибках. Этот цвет используется только если его не переопределяет компонент.","Общий цвет границ для элементов с фокусом. Этот цвет используется только в том случае, если не переопределен в компоненте.","Дополнительная граница вокруг элементов, которая отделяет их от других элементов для улучшения контраста.","Дополнительная граница вокруг активных элементов, которая отделяет их от других элементов для улучшения контраста.","Цвет переднего плана для ссылок в тексте.","Цвет фона для программного кода в тексте.",'Цвет тени мини-приложений редактора, таких как "Найти/заменить".',"Фон поля ввода.","Передний план поля ввода.","Граница поля ввода.","Цвет границ активированных параметров в полях ввода.","Цвет фона активированных параметров в полях ввода.",'Фоновый цвет проверки ввода для уровня серьезности "Сведения".','Цвет переднего плана области проверки ввода для уровня серьезности "Сведения".','Цвет границы проверки ввода для уровня серьезности "Сведения".','Фоновый цвет проверки ввода для уровня серьезности "Предупреждение".','Цвет переднего плана области проверки ввода для уровня серьезности "Предупреждение".','Цвет границы проверки ввода для уровня серьезности "Предупреждение".','Фоновый цвет проверки ввода для уровня серьезности "Ошибка".','Цвет переднего плана области проверки ввода для уровня серьезности "Ошибка".','Цвет границы проверки ввода для уровня серьезности "Ошибка".',"Фон раскрывающегося списка.","Передний план раскрывающегося списка.","Цвет средства быстрого выбора для группировки меток.","Цвет средства быстрого выбора для группировки границ.","Цвет фона бэджа. Бэджи - небольшие информационные элементы,  отображающие количество, например, результатов поиска.","Цвет текста бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.","Цвет тени полосы прокрутки, которая свидетельствует о том, что содержимое прокручивается.","Цвет фона для ползунка полосы прокрутки.","Цвет фона ползунка полосы прокрутки при наведении курсора.","Цвет фона ползунка полосы прокрутки при щелчке по нему.","Цвет фона индикатора выполнения, который может отображаться для длительных операций.","Цвет волнистой линии для выделения ошибок в редакторе.","Цвет границы для окон ошибок в редакторе.","Цвет волнистой линии для выделения предупреждений в редакторе.","Цвет границы для окон предупреждений в редакторе.","Цвет волнистой линии для выделения информационных сообщений в редакторе.","Цвет границы для окон сведений в редакторе.","Цвет волнистой линии для выделения подсказок в редакторе.","Цвет границы для окон указаний в редакторе.","Цвет фона редактора.","Цвет переднего плана редактора по умолчанию.","Цвет фона виджетов редактора, таких как найти/заменить.",'Цвет переднего плана мини-приложений редактора, таких как "Поиск/замена".',"Цвет границы мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница и если этот цвет не переопределен мини-приложением.","Цвет границы панели изменения размера мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница для изменения размера и если этот цвет не переопределен мини-приложением.","Цвет выделения редактора.","Цвет выделенного текста в режиме высокого контраста.","Цвет выделения в неактивном редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет для областей, содержимое которых совпадает с выбранным фрагментом. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет границы регионов с тем же содержимым, что и в выделении.","Цвет текущего поиска совпадений.","Цвет других совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет границы текущего результата поиска.","Цвет границы других результатов поиска.","Цвет границы для диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Выделение под словом, для которого отображается меню при наведении курсора. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет фона при наведении указателя на редактор.","Цвет переднего плана для наведения указателя на редактор.","Цвет границ при наведении указателя на редактор.","Цвет фона строки состояния при наведении в редакторе.","Цвет активных ссылок.","Цвет, используемый для значка действий в меню лампочки.","Цвет, используемый для значка действий автоматического исправления в меню лампочки.","Цвет фона для вставленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет фона для удаленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет контура для добавленных строк.","Цвет контура для удаленных строк.","Цвет границы между двумя текстовыми редакторами.","Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Цвет переднего плана находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Цвет переднего плана выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Цвет текста выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.","Фоновый цвет элементов List/Tree при наведении курсора мыши.","Цвет переднего плана элементов List/Tree при наведении курсора мыши.","Фоновый цвет элементов List/Tree при перемещении с помощью мыши.","Цвет переднего плана для выделения соответствия при поиске по элементу List/Tree.","Цвет фона для мини-приложения фильтра типов в списках и деревьях.","Цвет контура для мини-приложения фильтра типов в списках и деревьях.","Цвет контура для мини-приложения фильтра типов в списках и деревьях при отсутствии совпадений.","Цвет штриха дерева для направляющих отступа.","Цвет границ меню.","Цвет переднего плана пунктов меню.","Цвет фона пунктов меню.","Цвет переднего плана выбранного пункта меню в меню.","Цвет фона для выбранного пункта в меню.","Цвет границы для выбранного пункта в меню.","Цвет разделителя меню в меню.","Цвет фона выделения в позиции табуляции фрагмента.","Цвет границы выделения в позиции табуляции фрагмента.","Цвет фона выделения в последней позиции табуляции фрагмента.","Цвет границы выделения в последней позиции табуляции фрагмента. ","Цвет маркера обзорной линейки для совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Маркер обзорной линейки для выделения выбранного фрагмента. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.","Цвет маркера мини-карты для поиска совпадений.","Цвет маркера мини-карты для выбора редактора.","Цвет маркера миникарты для ошибок.","Цвет маркера миникарты для предупреждений.","Цвет, используемый для значка ошибки, указывающего на наличие проблем.","Цвет, используемый для предупреждающего значка, указывающего на наличие проблем.","Цвет, используемый для информационного значка, указывающего на наличие проблем."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.ru.js.map