<template>
  <div class="code-editor">
    <textarea
      v-model="codeValue"
      :readonly="readonly"
      :style="{ width: width, height: height }"
      class="code-textarea"
    />
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  readonly: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '200px'
  },
  lang: {
    type: String,
    default: 'javascript'
  },
  theme: {
    type: String,
    default: 'vs'
  }
})

const emit = defineEmits(['update:modelValue'])

const codeValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.code-editor {
  border: 1px solid #ddd;
  border-radius: 4px;
}

.code-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: vertical;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 10px;
  background-color: #f5f5f5;
}

.code-textarea[readonly] {
  background-color: #f9f9f9;
  cursor: default;
}
</style>