import SvgIcon from '@/components/SvgIcon/index.vue'// svg component

// 导出组件注册函数
export function registerSvgIcon(app) {
  // register globally
  app.component('svg-icon', SvgIcon)

  // 使用import.meta.glob替代require.context
  const modules = import.meta.glob('./svg/*.svg', { eager: true })
  Object.keys(modules).forEach((path) => {
    const moduleName = path.replace(/\.\/svg\/(.*)\.svg/, '$1')
    // 这里可以处理SVG文件的注册逻辑
  })
}
