<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key"></router-view>
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { useTagsViewStore } from '@/store'
export default {
  name: 'AppMain',
  data() {
    return {
      tagsViewStore: null
    }
  },
  created() {
    this.tagsViewStore = useTagsViewStore()
  },
  computed: {
    cachedViews() {
      // 新增tagsview
      return this.tagsViewStore ? this.tagsViewStore.cachedViews : []
    },
    key() {
      return this.$route.path
    }
  }
}
</script>
<style lang="scss" scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>
