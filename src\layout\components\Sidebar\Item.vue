<template>
  <div>
    <el-icon v-if="icon && isElementPlusIcon(icon)">
      <component :is="icon" />
    </el-icon>
    <SvgIcon v-else-if="icon" :icon-class="icon" />
    <span v-if="title">{{ title }}</span>
  </div>
</template>

<script setup>
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

defineProps({
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  }
})

const isElementPlusIcon = (iconName) => {
  return Object.keys(ElementPlusIconsVue).includes(iconName)
}
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
</style>