/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.20.0(6363745c0a33c27b149b89342a7b96d354fb554c)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.es",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (ocurrió de nuevo)","{0} (ocurrido {1} veces)"],"vs/base/browser/ui/findinput/findInput":["Entrada"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Coincidir mayúsculas y minúsculas","Solo palabras completas","Usar expresión regular"],"vs/base/browser/ui/findinput/replaceInput":["Entrada","Conservar may/min"],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Advertencia: {0}","Información: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Sin enlazar"],"vs/base/browser/ui/list/listWidget":["{0}. Para navegar utilice las teclas de navegación."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Borrar","Desactivar filtro en tipo","Activar filtro en el tipo","No se encontraron elementos","{0} de {1} elementos coincidentes"],
"vs/base/common/keybindingLabels":["Ctrl","Mayús","Alt","Windows","Ctrl","Mayús","Alt","Super","Control","Mayús","Alt","Comando","Control","Mayús","Alt","Windows","Control","Mayús","Alt","Super"],"vs/base/common/severity":["Error","Advertencia","Información"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, selector","selector"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Selector rápido. Escriba para restringir los resultados.","Selector rápido","{0} resultados"],"vs/editor/browser/controller/coreCommands":["&&Seleccionar todo","&&Deshacer","&&Rehacer"],"vs/editor/browser/controller/textAreaHandler":["No se puede acceder al editor en este momento. Presione Alt+F1 para ver opciones."],"vs/editor/browser/widget/codeEditorWidget":["El número de cursores se ha limitado a {0}."],"vs/editor/browser/widget/diffEditorWidget":["Los archivos no se pueden comparar porque uno de ellos es demasiado grande."],
"vs/editor/browser/widget/diffReview":["Cerrar","sin líneas","1 línea","{0} líneas","Diferencia {0} de {1}: original {2}, {3}, modificado {4}, {5}","vacío","original {0}, modificado {1}: {2}","+ modificado {0}: {1}","- original {0}: {1}","Ir a la siguiente diferencia","Ir a la diferencia anterior"],"vs/editor/browser/widget/inlineDiffMargin":["Copiar líneas eliminadas","Copiar línea eliminada","Copiar la línea eliminada ({0})","Revertir este cambio","Copiar la línea eliminada ({0})"],
"vs/editor/common/config/commonEditorConfig":["Editor",'El número de espacios a los que equivale una tabulación. Este valor se invalida en función del contenido del archivo cuando "#editor.detectIndentation#" está activado.','Insertar espacios al presionar "TAB". Este valor se invalida en función del contenido del archivo cuando "#editor.detectIndentation#" está activado. ','Controla si "#editor.tabSize#" y "#editor.insertSpaces#" se detectarán automáticamente al abrir un archivo en función del contenido de este.',"Quitar el espacio en blanco final autoinsertado.","Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.","Habilita sugerencias basadas en palabras.","Controls whether the semanticHighlighting is shown for the languages that support it.",'Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar "Escape".',"Las lineas por encima de esta longitud no se tokenizarán por razones de rendimiento.","Tiempo de espera en milisegundos después del cual se cancela el cálculo de diferencias. Utilice 0 para no usar tiempo de espera.","Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.","Controla si el editor de diferencias muestra los cambios de espacio inicial o espacio final como diferencias.","Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados."],
"vs/editor/common/config/editorOptions":["El editor usará API de plataforma para detectar cuándo está conectado un lector de pantalla.","El editor se optimizará de forma permanente para su uso con un editor de pantalla.","El editor nunca se optimizará para su uso con un lector de pantalla.","Controla si el editor se debe ejecutar en un modo optimizado para lectores de pantalla.","Controla si se inserta un carácter de espacio al comentar.","Controla si al copiar sin selección se copia la línea actual.","Controla si la cadena de búsqueda del widget de búsqueda se inicializa desde la selección del editor.","No activar nunca Buscar en la selección automáticamente (predeterminado)","Activar siempre automáticamente Buscar en la selección","Active Buscar en la selección automáticamente cuando se seleccionen varias líneas de contenido.","Controla si la operación de búsqueda se lleva a cabo en el texto seleccionado o el archivo entero en el editor.","Controla si el widget de búsqueda debe leer o modificar el Portapapeles de búsqueda compartido en macOS.","Controla si Encontrar widget debe agregar más líneas en la parte superior del editor. Si es true, puede desplazarse más allá de la primera línea cuando Encontrar widget está visible.","Habilita o deshabilita las ligaduras tipográficas.","Configuración explícita de las características de fuente.","Configura ligaduras de fuentes.","Controla el tamaño de fuente en píxeles.","Mostrar vista de inspección de los resultados (predeterminado)","Ir al resultado principal y mostrar una vista de inspección","Vaya al resultado principal y habilite la navegación sin peek para otros",'Esta configuración está en desuso. Use configuraciones separadas como "editor.editor.gotoLocation.multipleDefinitions" o "editor.editor.gotoLocation.multipleImplementations" en su lugar.','Controla el comportamiento del comando "Ir a definición" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a definición de tipo" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a declaración" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a implementaciones" cuando existen varias ubicaciones de destino.','Controla el comportamiento del comando "Ir a referencias" cuando existen varias ubicaciones de destino.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a definición" es la ubicación actual.','Id. de comando alternativo que se está ejecutando cuando el resultado de "Ir a definición de tipo" es la ubicación actual.','Id. de comando alternativo que se está ejecutando cuando el resultado de "Ir a declaración" es la ubicación actual.','Id. de comando alternativo que se está ejecutando cuando el resultado de "Ir a implementación" es la ubicación actual.','Identificador de comando alternativo que se ejecuta cuando el resultado de "Ir a referencia" es la ubicación actual.',"Controla si se muestra la información al mantener el puntero sobre un elemento.","Controla el retardo en milisegundos después del cual se muestra la información al mantener el puntero sobre un elemento.","Controla si la información que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.","Habilita la bombilla de acción de código en el editor.","Controla la altura de línea. Usa 0 para utilizar la altura del tamaño de fuente.","Controla si se muestra el minimapa.","Controla en qué lado se muestra el minimapa.","Controla cuándo se muestra el control deslizante del minimapa.","Escala del contenido dibujado en el minimapa.","Represente los caracteres reales en una línea, por oposición a los bloques de color.","Limite el ancho del minimapa para representar como mucho un número de columnas determinado.","Habilita un elemento emergente que muestra documentación de los parámetros e información de los tipos mientras escribe.","Controla si el menú de sugerencias de parámetros se cicla o se cierra al llegar al final de la lista.","Habilita sugerencias rápidas en las cadenas.","Habilita sugerencias rápidas en los comentarios.","Habilita sugerencias rápidas fuera de las cadenas y los comentarios.","Controla si deben mostrarse sugerencias automáticamente mientras se escribe.","Los números de línea no se muestran.","Los números de línea se muestran como un número absoluto.","Los números de línea se muestran como distancia en líneas a la posición del cursor.","Los números de línea se muestran cada 10 líneas.","Controla la visualización de los números de línea.","Muestra reglas verticales después de un cierto número de caracteres monoespaciados. Usa múltiples valores para mostrar múltiples reglas. Si la matriz está vacía, no se muestran reglas.","Inserte la sugerencia sin sobrescribir el texto a la derecha del cursor.","Inserte la sugerencia y sobrescriba el texto a la derecha del cursor.","Controla si las palabras se sobrescriben al aceptar la finalización. Tenga en cuenta que esto depende de las extensiones que participan en esta característica.",'Controla si se deben destacar las modificaciones inesperadas en el texto mientras se aceptan las finalizaciones, por ejemplo, "insertMode" es "replace", pero la finalización solo es compatible con "insert".',"Controla si el filtrado y la ordenación de sugerencias se tienen en cuenta para los errores ortográficos pequeños.","Controla si la ordenación de palabras mejora lo que aparece cerca del cursor.",'Controla si las selecciones de sugerencias recordadas se comparten entre múltiples áreas de trabajo y ventanas (necesita "#editor.suggestSelection#").',"Controla si un fragmento de código activo impide las sugerencias rápidas.","Controla si mostrar u ocultar iconos en sugerencias.","Controla cuántas sugerencias mostrará IntelliSense antes de que aparezca una barra de desplazamiento (máximo 15).",'Esta configuración está en desuso. Use configuraciones separadas como "editor.suggest.showKeyword" o "editor.suggest.showSnippets" en su lugar.','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "method".','Cuando está habilitado, IntelliSense muestra sugerencias de "función".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "constructor".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "field".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "variable".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "class".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "struct".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "interface".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "module".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "property".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "event".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "operator".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "unit".','Cuando está habilitado, IntelliSense muestra sugerencias de "value".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "constant".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "enum".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "enumMember".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "keyword".','Si está habilitado, IntelliSense muestra sugerencias de tipo "text".','Cuando está habilitado, IntelliSense muestra sugerencias de "color".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "file".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "reference".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "customcolor".','Si está habilitado, IntelliSense muestra sugerencias de tipo "folder".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "typeParameter".','Cuando está habilitado, IntelliSense muestra sugerencias de tipo "snippet".',"Controla la visibilidad de la barra de estado en la parte inferior del widget de sugerencias.",'Controla si se deben aceptar sugerencias en los caracteres de confirmación. Por ejemplo, en Javascript, el punto y coma (";") puede ser un carácter de confirmación que acepta una sugerencia y escribe ese carácter.','Aceptar solo una sugerencia con "Entrar" cuando realiza un cambio textual.','Controla si las sugerencias deben aceptarse con "Entrar", además de "TAB". Ayuda a evitar la ambigüedad entre insertar nuevas líneas o aceptar sugerencias.',"Controla el número de líneas en el editor que puede leer un lector de pantalla. Advertencia: Esto puede afectar al rendimiento de números superiores al predeterminado.","Contenido del editor","Utilizar las configuraciones del lenguaje para determinar cuándo cerrar los corchetes automáticamente.","Cerrar automáticamente los corchetes cuando el cursor esté a la izquierda de un espacio en blanco.","Controla si el editor debe cerrar automáticamente los corchetes después de que el usuario agregue un corchete de apertura.","Escriba en las comillas o los corchetes solo si se insertaron automáticamente.","Controla si el editor debe escribir entre comillas o corchetes.","Utilizar las configuraciones del lenguaje para determinar cuándo cerrar las comillas automáticamente. ","Cerrar automáticamente las comillas cuando el cursor esté a la izquierda de un espacio en blanco. ","Controla si el editor debe cerrar automáticamente las comillas después de que el usuario agrega uma comilla de apertura.","El editor no insertará la sangría automáticamente.","El editor mantendrá la sangría de la línea actual.","El editor respetará la sangría de la línea actual y los corchetes definidos por el idioma.","El editor mantendrá la sangría de la línea actual, respetará los corchetes definidos por el idioma e invocará onEnterRules especiales definidos por idiomas.","El editor respetará la sangría de la línea actual, los corchetes definidos por idiomas y las reglas indentationRules definidas por idiomas, además de invocar reglas onEnterRules especiales.","Controla si el editor debe ajustar automáticamente la sangría mientras los usuarios escriben, pegan, mueven o sangran líneas.","Use las configuraciones de idioma para determinar cuándo delimitar las selecciones automáticamente.","Envolver con comillas, pero no con corchetes.","Envolver con corchetes, pero no con comillas.","Controla si el editor debe delimitar automáticamente las selecciones.","Controla si el editor muestra CodeLens.","Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en línea.","Controla si el resaltado de sintaxis debe ser copiado al portapapeles.","Controla el estilo de animación del cursor.","Controla si la animación suave del cursor debe estar habilitada.","Controla el estilo del cursor.",'Controla el número mínimo de líneas iniciales y finales visibles que rodean al cursor. Se conoce como "scrollOff" o "scrollOffset" en algunos otros editores.','Solo se aplica "cursorSurroundingLines" cuando se desencadena mediante el teclado o la API.','"cursorSurroundingLines" se aplica siempre.','Controla cuando se debe aplicar "cursorSurroundingLines".','Controla el ancho del cursor cuando "#editor.cursorStyle#" se establece en "line".',"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.",'Multiplicador de la velocidad de desplazamiento al presionar "Alt".',"Controla si el editor tiene el plegado de código habilitado.",'Controla la estrategia para calcular los intervalos de plegado. "auto" usa una estrategia de plegado específica del idioma, si está disponible. "indentation" usa la estrategia de plegado basada en sangría.',"Controla si el editor debe destacar los rangos plegados.","Controla la familia de fuentes.","Controla el grosor de la fuente.","Controla si el editor debe dar formato automáticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. ","Controla si el editor debe dar formato a la línea automáticamente después de escribirla.","Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuración.","Controla si el cursor debe ocultarse en la regla de información general.","Controla si el editor debe resaltar la guía de sangría activa.","Controla el espacio entre letras en pixels.","Controla si el editor debe detectar vínculos y hacerlos interactivos.","Resaltar paréntesis coincidentes.",'Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse "deltaX" y "deltaY". ','Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona "Ctrl".',"Combinar varios cursores cuando se solapan.",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opción" en macOS.',"El modificador que se usará para agregar varios cursores con el mouse. Los gestos del mouse Ir a definición y Abrir vínculo se adaptarán de modo que no entren en conflicto con el modificador multicursor. [Más información](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Cada cursor pega una única línea del texto.","Cada cursor pega el texto completo.","Controla el pegado cuando el recuento de líneas del texto pegado coincide con el recuento de cursores.","Controla si el editor debe resaltar las apariciones de símbolos semánticos.","Controla si debe dibujarse un borde alrededor de la regla de información general.","Enfocar el árbol al abrir la vista","Enfocar el editor al abrir la inspección","Controla si se debe enfocar el editor en línea o el árbol en el widget de vista.","Controla el retraso, en milisegundos, tras el cual aparecerán sugerencias rápidas.","Controla si el editor debe representar caracteres de control.","Controla si el editor debe representar guías de sangría.","Representar el número de la última línea cuando el archivo termina con un salto de línea.","Resalta el medianil y la línea actual.","Controla cómo debe representar el editor el resaltado de línea actual.","Render whitespace characters except for single spaces between words.","Represente los caracteres de espacio en blanco solo en el texto seleccionado.","Controla la forma en que el editor debe representar los caracteres de espacio en blanco.","Controla si las selecciones deberían tener las esquinas redondeadas.","Controla el número de caracteres adicionales a partir del cual el editor se desplazará horizontalmente.","Controla si el editor seguirá haciendo scroll después de la última línea.","Controla si el portapapeles principal de Linux debe admitirse.","Controla si el editor debe destacar las coincidencias similares a la selección.","Controla cuándo los controles de plegado del margen son ocultados automáticamente.","Controla el fundido de salida del código no usado.","Mostrar sugerencias de fragmentos de código por encima de otras sugerencias.","Mostrar sugerencias de fragmentos de código por debajo de otras sugerencias.","Mostrar sugerencias de fragmentos de código con otras sugerencias.","No mostrar sugerencias de fragmentos de código.","Controla si se muestran los fragmentos de código con otras sugerencias y cómo se ordenan.","Controla si el editor se desplazará con una animación.","Tamaño de la fuente para el widget de sugerencias. Cuando se establece a `0`, se utilizará el valor `#editor.fontSize#`.","Altura de la línea del widget de sugerencias. Cuando se establece a `0`, se utiliza el valor `#editor.lineHeight#`.","Controla si deben aparecer sugerencias de forma automática al escribir caracteres desencadenadores.","Seleccionar siempre la primera sugerencia.",'Seleccione sugerencias recientes a menos que al escribir más se seleccione una, por ejemplo, "console.| -> console.log" porque "log" se ha completado recientemente.','Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, "co -> console" y "con -> const".',"Controla cómo se preseleccionan las sugerencias cuando se muestra la lista,","La pestaña se completará insertando la mejor sugerencia de coincidencia encontrada al presionar la pestaña","Deshabilitar los complementos para pestañas.","La pestaña se completa con fragmentos de código cuando su prefijo coincide. Funciona mejor cuando las 'quickSuggestions' no están habilitadas.","Habilita completar pestañas.","La inserción y eliminación del espacio en blanco sigue a las tabulaciones.","Caracteres que se usarán como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.","Las líneas no se ajustarán nunca.","Las líneas se ajustarán en el ancho de la ventanilla.",'Las líneas se ajustarán al valor de "#editor.wordWrapColumn#". ','Las líneas se ajustarán al valor que sea inferior: el tamaño de la ventanilla o el valor de "#editor.wordWrapColumn#".',"Controla cómo deben ajustarse las líneas.",'Controla la columna de ajuste del editor cuando "#editor.wordWrap#" es "wordWrapColumn" o "bounded".',"No hay sangría. Las líneas ajustadas comienzan en la columna 1.","A las líneas ajustadas se les aplica la misma sangría que al elemento primario.","A las líneas ajustadas se les aplica una sangría de +1 respecto al elemento primario.","A las líneas ajustadas se les aplica una sangría de +2 respecto al elemento primario.","Controla la sangría de las líneas ajustadas.","Se supone que todos los caracteres son del mismo ancho. Este es un algoritmo rápido que funciona correctamente para fuentes monoespaciales y ciertos scripts (como caracteres latinos) donde los glifos tienen el mismo ancho.","Delega el cálculo de puntos de ajuste en el explorador. Es un algoritmo lento, que podría causar bloqueos para archivos grandes, pero funciona correctamente en todos los casos.","Controla el algoritmo que calcula los puntos de ajuste."],
"vs/editor/common/modes/modesRegistry":["Texto sin formato"],
"vs/editor/common/standaloneStrings":["Sin selección","Línea {0}, columna {1} ({2} seleccionadas)","Línea {0}, columna {1}","{0} selecciones ({1} caracteres seleccionados)","{0} selecciones",'Se cambiará ahora el valor "accessibilitySupport" a "activado".',"Se abrirá ahora la página de documentación de accesibilidad del editor.","en un panel de solo lectura de un editor de diferencias.","en un panel de un editor de diferencias.","en un editor de código de solo lectura"," en un editor de código","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Comando+E.","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Control+E.","El editor está configurado para optimizarse para su uso con un lector de pantalla.","El editor está configurado para que no se optimice nunca su uso con un lector de pantalla, que en este momento no es el caso.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Al presionar TAB en el editor actual, se insertará el carácter de tabulación. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, se insertará el carácter de tabulación. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Presione ahora Comando+H para abrir una ventana del explorador con más información relacionada con la accesibilidad del editor.","Presione ahora Control+H para abrir una ventana del explorador con más información relacionada con la accesibilidad del editor.","Para descartar esta información sobre herramientas y volver al editor, presione Esc o Mayús+Escape.","Mostrar ayuda de accesibilidad","Desarrollador: inspeccionar tokens","Ir a la línea {0} y al carácter {1}","Ir a la línea {0}","Escriba un número de línea comprendido entre 1 y {0} a la cual quiera navegar.","Escriba un carácter entre 1 y {0} para ir a","Línea actual: {0}. ir a la línea {1}.","Escriba un número de línea, seguido de un signo opcional de dos puntos y un número de caracteres para desplazarse a","Ir a la línea...","{0}, {1}, comandos","{0}, comandos","Escriba el nombre de una acción que desee ejecutar","Paleta de comandos","{0}, símbolos","Escriba el nombre de un identificador al que quiera ir","Ir a símbolo...","símbolos ({0})","módulos ({0})","clases ({0})","interfaces ({0})","métodos ({0})","funciones ({0})","propiedades ({0})","variables ({0})","variables ({0})","constructores ({0})","llama a ({0})","Contenido del editor","Presione Ctrl+F1 para ver las opciones de accesibilidad.","Presione Alt+F1 para ver las opciones de accesibilidad.","Alternar tema de contraste alto","{0} ediciones realizadas en {1} archivos"],
"vs/editor/common/view/editorColorRegistry":["Color de fondo para la línea resaltada en la posición del cursor.","Color de fondo del borde alrededor de la línea en la posición del cursor.","Color de fondo de rangos resaltados, como en abrir rápido y encontrar características. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo del borde alrededor de los intervalos resaltados.","Color de fondo del símbolo destacado, como Ir a definición o Ir al siguiente/anterior símbolo. El color no debe ser opaco para no ocultar la decoración subyacente.","Color de fondo del borde alrededor de los símbolos resaltados.","Color del cursor del editor.","Color de fondo del cursor de edición. Permite personalizar el color del caracter solapado por el bloque del cursor.","Color de los caracteres de espacio en blanco del editor.","Color de las guías de sangría del editor.","Color de las guías de sangría activas del editor.","Color de números de línea del editor.","Color del número de línea activa en el editor","ID es obsoleto. Usar en lugar 'editorLineNumber.activeForeground'. ","Color del número de línea activa en el editor","Color de las reglas del editor","Color principal de lentes de código en el editor","Color de fondo tras corchetes coincidentes","Color de bloques con corchetes coincidentes","Color del borde de la regla de visión general.","Color de fondo del margen del editor. Este espacio contiene los márgenes de glifos y los números de línea.","Color del borde de código fuente innecesario (sin usar) en el editor.","Opacidad de código fuente innecesario (sin usar) en el editor. Por ejemplo, \"#000000c0\" representará el código con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema 'editorUnnecessaryCode.border' para resaltar el código innecesario en vez de atenuarlo.","Color de marcador de regla de información general para errores. ","Color de marcador de regla de información general para advertencias.","Color de marcador de regla de información general para mensajes informativos. "],
"vs/editor/contrib/bracketMatching/bracketMatching":["Resumen color de marcador de regla para corchetes.","Ir al corchete","Seleccionar para corchete","Ir al &&corchete"],"vs/editor/contrib/caretOperations/caretOperations":["Mover símbolo de inserción a la izquierda","Mover símbolo de inserción a la derecha"],"vs/editor/contrib/caretOperations/transpose":["Transponer letras"],"vs/editor/contrib/clipboard/clipboard":["Cortar","Cor&&tar","Copiar","C&&opiar","Pegar","&&Pegar","Copiar con resaltado de sintaxis"],
"vs/editor/contrib/codeAction/codeActionCommands":["Tipo de la acción de código que se va a ejecutar.","Controla cuándo se aplican las acciones devueltas.","Aplicar siempre la primera acción de código devuelto.","Aplicar la primera acción de código devuelta si solo hay una.","No aplique las acciones de código devuelto.","Controla si solo se deben devolver las acciones de código preferidas.","Se ha producido un error desconocido al aplicar la acción de código","Corrección Rápida","No hay acciones de código disponibles",'No hay acciones de código preferidas para "{0}" disponibles','No hay ninguna acción de código para "{0}" disponible.',"No hay acciones de código preferidas disponibles","No hay acciones de código disponibles","Refactorizar...",'No hay refactorizaciones preferidas de "{0}" disponibles','No hay refactorizaciones de "{0}" disponibles',"No hay ninguna refactorización favorita disponible.","No hay refactorizaciones disponibles","Acción de Origen...",'No hay acciones de origen preferidas para "{0}" disponibles','No hay ninguna acción de origen para "{0}" disponible.',"No hay ninguna acción de origen favorita disponible.","No hay acciones de origen disponibles","Organizar Importaciones","No hay acciones de importación disponibles","Corregir todo","No está disponible la acción de corregir todo","Corregir automáticamente...","No hay autocorrecciones disponibles"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Mostrar correcciones. Solución preferida disponible ({0})","Mostrar correcciones ({0})","Mostrar correcciones"],"vs/editor/contrib/comment/comment":["Alternar comentario de línea","&&Alternar comentario de línea","Agregar comentario de línea","Quitar comentario de línea","Alternar comentario de bloque","Alternar &&bloque de comentario"],"vs/editor/contrib/contextmenu/contextmenu":["Mostrar menú contextual del editor"],"vs/editor/contrib/cursorUndo/cursorUndo":["Cursor Deshacer","Cursor Rehacer"],
"vs/editor/contrib/documentSymbols/outlineTree":["Color de primer plano de los símbolos de matriz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos booleanos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de clase. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de color. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos constantes. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de constructor. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de miembro del enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de evento. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de campo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de archivo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de carpeta. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de función. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de interfaz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de claves. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de palabra clave. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de método. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de módulo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de espacio de nombres. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos nulos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano para los símbolos numéricos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de objeto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano para los símbolos del operador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de paquete. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de propiedad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de referencia. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de fragmento de código. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de cadena. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de estructura. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de texto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano para los símbolos de parámetro de tipo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos de unidad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.","Color de primer plano de los símbolos variables. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias."],
"vs/editor/contrib/find/findController":["Buscar","&&Buscar","Buscar con selección","Buscar siguiente","Buscar siguiente","Buscar anterior","Buscar anterior","Buscar selección siguiente","Buscar selección anterior","Reemplazar","&&Reemplazar"],"vs/editor/contrib/find/findWidget":["Buscar","Buscar","Coincidencia anterior","Próxima coincidencia","Buscar en selección","Cerrar","Reemplazar","Reemplazar","Reemplazar","Reemplazar todo","Alternar modo de reemplazar","Sólo los primeros {0} resultados son resaltados, pero todas las operaciones de búsqueda trabajan en todo el texto.","{0} de {1}","No hay resultados","Encontrados: {0}","Encontrados: {0} para {1}","Encontrados: {0} para {1} en {2}","Encontrados: {0} para {1}","Ctrl+Entrar ahora inserta un salto de línea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento."],
"vs/editor/contrib/folding/folding":["Desplegar","Desplegar de forma recursiva","Plegar","Alternar plegado","Plegar de forma recursiva","Cerrar todos los comentarios de bloque","Plegar todas las regiones","Desplegar Todas las Regiones","Plegar todo","Desplegar todo","Nivel de plegamiento {0}","Color de la selección del editor."],"vs/editor/contrib/fontZoom/fontZoom":["Acercarse a la tipografía del editor","Alejarse de la tipografía del editor","Restablecer alejamiento de la tipografía del editor"],"vs/editor/contrib/format/format":["1 edición de formato en la línea {0}","{0} ediciones de formato en la línea {1}","1 edición de formato entre las líneas {0} y {1}","{0} ediciones de formato entre las líneas {1} y {2}"],"vs/editor/contrib/format/formatActions":["Dar formato al documento","Dar formato a la selección"],
"vs/editor/contrib/gotoError/gotoError":["Ir al siguiente problema (Error, Advertencia, Información)","Ir al problema anterior (Error, Advertencia, Información)","Ir al siguiente problema en Archivos (Error, Advertencia, Información)","Ir al problema anterior en Archivos (Error, Advertencia, Información)","Siguiente &&problema","Anterior &&problema"],"vs/editor/contrib/gotoError/gotoErrorWidget":["{0} de {1} problemas","{0} de {1} problema","Color de los errores del widget de navegación de marcadores del editor.","Color de las advertencias del widget de navegación de marcadores del editor.","Color del widget informativo marcador de navegación en el editor.","Fondo del widget de navegación de marcadores del editor."],
"vs/editor/contrib/gotoSymbol/goToCommands":["Ver","Definiciones",'No se encontró ninguna definición para "{0}"',"No se encontró ninguna definición","Ir a definición","Ir a &&definición","Abrir definición en el lateral","Ver la definición","Declaraciones","No se encontró ninguna definición para '{0}'","No se encontró ninguna declaración","Ir a Definición","Ir a &&Declaración","No se encontró ninguna definición para '{0}'","No se encontró ninguna declaración","Inspeccionar Definición","Definiciones de tipo",'No se encontró ninguna definición de tipo para "{0}"',"No se encontró ninguna definición de tipo","Ir a la definición de tipo","Ir a la definición de &&tipo","Inspeccionar definición de tipo","Implementaciones",'No se encontró ninguna implementación para "{0}"',"No se encontró ninguna implementación","Ir a Implementaciones","Ir a &&Implementaciones","Inspeccionar implementaciones",'No se ha encontrado ninguna referencia para "{0}".',"No se encontraron referencias","Ir a Referencias","Ir a &&Referencias","Referencias","Inspeccionar Referencias","Referencias","Ir a cualquier símbolo","Ubicaciones",'No hay ningún resultado para "{0}".',"Referencias"],
"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Haga clic para mostrar {0} definiciones."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Cargando...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["Error al resolver el archivo.","{0} referencias","{0} referencia"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["vista previa no disponible","Referencias","No hay resultados","Referencias"],"vs/editor/contrib/gotoSymbol/referencesModel":["símbolo en {0} linea {1} en la columna {2}","1 símbolo en {0}, ruta de acceso completa {1}","{0} símbolos en {1}, ruta de acceso completa {2}","No se encontraron resultados","Encontró 1 símbolo en {0}","Encontró {0} símbolos en {1}","Encontró {0} símbolos en {1} archivos"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Símbolo {0} de {1}, {2} para el siguiente","Símbolo {0} de {1}"],"vs/editor/contrib/hover/hover":["Mostrar al mantener el puntero","Mostrar vista previa de la definición que aparece al mover el puntero"],
"vs/editor/contrib/hover/modesContentHover":["Cargando...","Ver problema","Buscando correcciones rápidas...","No hay correcciones rápidas disponibles","Corrección Rápida"],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Reemplazar con el valor anterior","Reemplazar con el valor siguiente"],"vs/editor/contrib/linesOperations/linesOperations":["Copiar línea arriba","&&Copiar línea arriba","Copiar línea abajo","Co&&piar línea abajo","Selección duplicada","&&Duplicar selección","Mover línea hacia arriba","Mo&&ver línea arriba","Mover línea hacia abajo","Mover &&línea abajo","Ordenar líneas en orden ascendente","Ordenar líneas en orden descendente","Recortar espacio final","Eliminar línea","Sangría de línea","Anular sangría de línea","Insertar línea arriba","Insertar línea debajo","Eliminar todo a la izquierda","Eliminar todo lo que está a la derecha","Unir líneas","Transponer caracteres alrededor del cursor","Transformar a mayúsculas","Transformar a minúsculas","Transformar en Title Case"],
"vs/editor/contrib/links/links":["Ejecutar comando","Seguir vínculo","cmd + clic","ctrl + clic","opción + clic","alt + clic","No se pudo abrir este vínculo porque no tiene un formato correcto: {0}","No se pudo abrir este vínculo porque falta el destino.","Abrir vínculo"],"vs/editor/contrib/message/messageController":["No se puede editar en un editor de sólo lectura"],
"vs/editor/contrib/multicursor/multicursor":["Agregar cursor arriba","&&Agregar cursor arriba","Agregar cursor debajo","A&&gregar cursor abajo","Añadir cursores a finales de línea","Agregar c&&ursores a extremos de línea","Añadir cursores a la parte inferior","Añadir cursores a la parte superior","Agregar selección hasta la siguiente coincidencia de búsqueda","Agregar &&siguiente repetición","Agregar selección hasta la anterior coincidencia de búsqueda","Agregar r&&epetición anterior","Mover última selección hasta la siguiente coincidencia de búsqueda","Mover última selección hasta la anterior coincidencia de búsqueda","Seleccionar todas las repeticiones de coincidencia de búsqueda","Seleccionar todas las &&repeticiones","Cambiar todas las ocurrencias"],"vs/editor/contrib/parameterHints/parameterHints":["Sugerencias para parámetros Trigger"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, sugerencia"],
"vs/editor/contrib/peekView/peekView":["Cerrar","Color de fondo del área de título de la vista de inspección.","Color del título de la vista de inpección.","Color de la información del título de la vista de inspección.","Color de los bordes y la flecha de la vista de inspección.","Color de fondo de la lista de resultados de vista de inspección.","Color de primer plano de los nodos de inspección en la lista de resultados.","Color de primer plano de los archivos de inspección en la lista de resultados.","Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspección.","Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspección.","Color de fondo del editor de vista de inspección.","Color de fondo del margen en el editor de vista de inspección.","Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspección.","Buscar coincidencia del color de resultado del editor de vista de inspección.","Hacer coincidir el borde resaltado en el editor de vista previa."],
"vs/editor/contrib/rename/rename":["No hay ningún resultado.","Error desconocido al resolver el cambio de nombre de la ubicación",'Cambiando el nombre de "{0}"',"Nombre cambiado correctamente de '{0}' a '{1}'. Resumen: {2}","No se pudo cambiar el nombre a las ediciones de aplicación","No se pudo cambiar el nombre de las ediciones de cálculo","Cambiar el nombre del símbolo","Activar/desactivar la capacidad de previsualizar los cambios antes de cambiar el nombre"],"vs/editor/contrib/rename/renameInputField":["Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar.","{0} para cambiar el nombre, {1} para obtener una vista previa"],"vs/editor/contrib/smartSelect/smartSelect":["Expandir selección","&&Expandir selección","Reducir la selección","&&Reducir selección"],
"vs/editor/contrib/snippet/snippetVariables":["Domingo","Lunes","Martes","Miércoles","Jueves","Viernes","Sábado","Dom","Lun","Mar","Mié","Jue","Vie","Sáb","Enero","Febrero","Marzo","Abril","May","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre","Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"],"vs/editor/contrib/suggest/suggestController":['Aceptando "{0}" ediciones adicionales de {1} realizadas',"Sugerencias para Trigger"],"vs/editor/contrib/suggest/suggestWidget":["Color de fondo del widget sugerido.","Color de borde del widget sugerido.","Color de primer plano del widget sugerido.","Color de fondo de la entrada seleccionada del widget sugerido.","Color del resaltado coincidido en el widget sugerido.","Leer más...{0}","Leer menos...{0}","Cargando...","Cargando...","No hay sugerencias.","{0} por menos...","{0} para más...","Elemento {0}, documentos: {1}","{0} para insertar, {1} para reemplazar","{0} para reemplazar, {1} para insertar","{0} para aceptar"],
"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Alternar tecla de tabulación para mover el punto de atención","Presionando la pestaña ahora moverá el foco al siguiente elemento enfocable.","Presionando la pestaña ahora insertará el carácter de tabulación"],"vs/editor/contrib/tokenization/tokenization":["Desarrollador: forzar nueva aplicación de token"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Color de fondo de un símbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo de un símbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo de un símbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.","Color de fondo de un símbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.","Color del marcador de regla general para destacados de símbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de marcador de regla general para destacados de símbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Ir al siguiente símbolo destacado","Ir al símbolo destacado anterior","Desencadenar los símbolos destacados"],
"vs/platform/configuration/common/configurationRegistry":["La configuración predeterminada se reemplaza","Establecer los valores de configuración que se reemplazarán para un lenguaje.","Esta configuración no admite la configuración por idioma.",'No se puede registrar "{0}". Coincide con el patrón de propiedad \'\\\\[.*\\\\]$\' para describir la configuración del editor específica del lenguaje. Utilice la contribución "configurationDefaults".','No se puede registrar "{0}". Esta propiedad ya está registrada.'],"vs/platform/keybinding/common/abstractKeybindingService":["Se presionó ({0}). Esperando la siguiente tecla...","La combinación de teclas ({0}, {1}) no es ningún comando."],
"vs/platform/list/browser/listService":["Área de trabajo",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opción" en macOS.',"El modificador que se utilizará para agregar un elemento en los árboles y listas para una selección múltiple con el ratón (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de ratón 'Abrir hacia' - si están soportados - se adaptarán de forma tal que no tenga conflicto con el modificador múltiple.","Controla cómo abrir elementos en árboles y listas usando el ratón (si está soportado). Para elementos padres con hijos en los árboles, esta configuración controlará si de un solo click o un doble click expande al elemento padre. Tenga en cuenta que algunos árboles y listas pueden optar por ignorar esta configuración si no se aplica.","Controla si las listas y los árboles admiten el desplazamiento horizontal en el área de trabajo.","Controla el esplazamiento horizontal de los árboles en la mesa de trabajo.",'Esta configuración está obsoleta, utilice "{0}" en su lugar.',"Controla la sangría de árbol en píxeles.","Controla si el árbol debe representar guías de sangría.","La navegación simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.","Destacar la navegación del teclado resalta los elementos que coinciden con la entrada del teclado. Más arriba y abajo la navegación atravesará solo los elementos destacados.","La navegación mediante el teclado de filtro filtrará y ocultará todos los elementos que no coincidan con la entrada del teclado.","Controla el estilo de navegación del teclado para listas y árboles en el área de trabajo. Puede ser simple, resaltar y filtrar.",'Controla si la navegación del teclado en listas y árboles se activa automáticamente simplemente escribiendo. Si se establece en "false", la navegación con el teclado solo se activa al ejecutar el comando "list.toggleKeyboardNavigation", para el cual puede asignar un método abreviado de teclado.'],
"vs/platform/markers/common/markers":["Error","Advertencia","Información"],
"vs/platform/theme/common/colorRegistry":["Color de primer plano general. Este color solo se usa si un componente no lo invalida.","Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.","Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.","Un borde adicional alrededor de los elementos para separarlos unos de otros y así mejorar el contraste.","Un borde adicional alrededor de los elementos activos para separarlos unos de otros y así mejorar el contraste.","Color de primer plano para los vínculos en el texto.","Color de fondo para los bloques de código en el texto.","Color de sombra de los widgets  dentro del editor, como buscar/reemplazar","Fondo de cuadro de entrada.","Primer plano de cuadro de entrada.","Borde de cuadro de entrada.","Color de borde de opciones activadas en campos de entrada.","Color de fondo de las opciones activadas en los campos de entrada.","Color de fondo de validación de entrada para gravedad de información.","Color de primer plano de validación de entrada para información de gravedad.","Color de borde de validación de entrada para gravedad de información.","Color de fondo de validación de entrada para gravedad de advertencia.","Color de primer plano de validación de entrada para información de advertencia.","Color de borde de validación de entrada para gravedad de advertencia.","Color de fondo de validación de entrada para gravedad de error.","Color de primer plano de validación de entrada para información de error.","Color de borde de valdación de entrada para gravedad de error.","Fondo de lista desplegable.","Primer plano de lista desplegable.","Selector de color rápido para la agrupación de etiquetas.","Selector de color rápido para la agrupación de bordes.","Color de fondo de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.","Color de primer plano de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.","Sombra de la barra de desplazamiento indica que la vista se ha despazado.","Color de fondo de control deslizante de barra de desplazamiento.","Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.","Color de fondo de la barra de desplazamiento al hacer clic.","Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duración.","Color de primer plano de squigglies de error en el editor.","Color del borde de los cuadros de error en el editor.","Color de primer plano de squigglies de advertencia en el editor.","Color del borde de los cuadros de advertencia en el editor.","Color de primer plano de los subrayados ondulados informativos en el editor.","Color del borde de los cuadros de información en el editor.","Color de primer plano de pista squigglies en el editor.","Color del borde de los cuadros de sugerencia en el editor.","Color de fondo del editor.","Color de primer plano predeterminado del editor.","Color de fondo del editor de widgets como buscar/reemplazar","Color de primer plano de los widgets del editor, como buscar y reemplazar.","Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.","Color del borde de la barra de cambio de tamaño de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tamaño y si un widget no invalida el color.","Color de la selección del editor.","Color del texto seleccionado para alto contraste.","Color de la selección en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color en las regiones con el mismo contenido que la selección. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de las regiones con el mismo contenido que la selección.","Color de la coincidencia de búsqueda actual.","Color de los otros resultados de la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de la coincidencia de búsqueda actual.","Color de borde de otra búsqueda que coincide.","Color del borde de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo al mantener el puntero en el editor.","Color de primer plano al mantener el puntero en el editor.","Color del borde al mantener el puntero en el editor.","Color de fondo de la barra de estado al mantener el puntero en el editor.","Color de los vínculos activos.","El color utilizado para el icono de bombilla de acciones.","El color utilizado para el icono de la bombilla de acciones de corrección automática.","Color de fondo para el texto que se insertó. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo para el texto que se eliminó. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de contorno para el texto insertado.","Color de contorno para el texto quitado.","Color del borde entre ambos editores de texto.","Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de primer plano de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol esta inactiva. Una lista o un árbol tiene el foco del teclado cuando está activo, cuando esta inactiva no.","Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.","Fondo de la lista o el árbol al mantener el mouse sobre los elementos.","Color de primer plano de la lista o el árbol al pasar por encima de los elementos con el ratón.","Fondo de arrastrar y colocar la lista o el árbol al mover los elementos con el mouse.","Color de primer plano de la lista o el árbol de las coincidencias resaltadas al buscar dentro de la lista o el ábol.","Color de fondo del widget de filtro de tipo en listas y árboles.","Color de contorno del widget de filtro de tipo en listas y árboles.","Color de contorno del widget de filtro de tipo en listas y árboles, cuando no hay coincidencias.","Color de trazo de árbol para las guías de sangría.","Color del borde de los menús.","Color de primer plano de los elementos de menú.","Color de fondo de los elementos de menú.","Color de primer plano del menu para el elemento del menú seleccionado.","Color de fondo del menu para el elemento del menú seleccionado.","Color del borde del elemento seleccionado en los menús.","Color del separador del menu para un elemento del menú.","Resaltado del color de fondo para una ficha de un fragmento de código.","Resaltado del color del borde para una ficha de un fragmento de código.","Resaltado del color de fondo para la última ficha de un fragmento de código.","Resaltado del color del borde para la última ficha de un fragmento de código.","Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del marcador de la regla general para los destacados de la selección. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de minimapa para coincidencias de búsqueda.","Color del marcador de minimapa para la selección del editor.","Color del marcador de minimapa para errores.","Color del marcador de minimapa para advertencias.","Color utilizado para el icono de error de problemas.","Color utilizado para el icono de advertencia de problemas.","Color utilizado para el icono de información de problemas."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.es.js.map