{"name": "FunDataFactory", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "vitest", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.8.2", "core-js": "3.39.0", "echarts": "5.5.1", "element-plus": "2.11.2", "js-cookie": "3.0.5", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-to-regexp": "8.2.0", "pinia": "2.3.0", "svg-baker-runtime": "^1.4.7", "uuid": "11.0.3", "vue": "3.5.21", "vue-clipboard3": "^2.0.0", "vue-monaco-editor": "1.0.0", "vue-router": "4.5.0"}, "devDependencies": {"@types/apidoc": "^0.50.3", "@vitejs/plugin-vue": "5.2.1", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/compiler-sfc": "3.5.21", "@vue/test-utils": "2.4.6", "autoprefixer": "10.4.20", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "5.4.1", "connect": "3.7.0", "crypto-js": "4.2.0", "eslint": "9.17.0", "eslint-plugin-vue": "9.32.0", "jsdom": "25.0.1", "mockjs": "1.1.0", "runjs": "4.3.2", "sass": "1.83.1", "sass-loader": "16.0.4", "serve-static": "2.1.0", "svgo": "3.3.2", "unplugin-auto-import": "0.18.6", "unplugin-vue-components": "0.28.0", "vite-svg-loader": "^5.1.0", "vitest": "2.1.8"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT", "packageManager": "pnpm@10.8.1+sha512.c50088ba998c67b8ca8c99df8a5e02fd2ae2e2b29aaf238feaa9e124248d3f48f9fb6db2424949ff901cffbb5e0f0cc1ad6aedb602cd29450751d11c35023677"}