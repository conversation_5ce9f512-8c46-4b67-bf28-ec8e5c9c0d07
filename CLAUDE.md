# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

FunDataFactory 是一个基于 Vue.js 3 + Element Plus 的测试平台前端项目，专为测试团队设计，用于测试脚本管理和执行。项目采用前后端分离架构，后端使用 Python + FastAPI。

## 核心开发命令

```bash
# 开发环境启动（端口9528）
npm run dev

# 代码检查
npm run lint

# 运行单元测试
npm run test:unit

# 生产环境构建
npm run build:prod

# 预发布环境构建
npm run build:stage

# 图标优化
npm run svgo

# 运行单个测试文件
npm run test:unit -- tests/unit/components/Breadcrumb.spec.js
```

## 技术栈与架构

### 前端技术栈
- **Vue.js 3.5.21** - 主框架
- **Element Plus 2.11.2** - UI组件库
- **Vue Router 4.5.0** - 路由管理
- **Pinia 2.3.0** - 状态管理
- **Axios 1.8.2** - HTTP客户端
- **Monaco Editor** - 代码编辑器（vue-monaco-editor 1.0.0）
- **ECharts 5.5.1** - 数据可视化
- **Vite** - 构建工具

### 项目结构
```
src/
├── api/          # API接口定义（user.js, project.js, case.js, data.js）
├── components/   # 公共组件（Breadcrumb, Hamburger, SvgIcon, Pagination, Code/monaco.vue）
├── layout/       # 布局组件（Sidebar, Navbar, TagsView, AppMain）
├── router/       # 路由配置（index.js）
├── store/        # Pinia状态管理（modules: app, user, settings, tagsView）
├── styles/       # 全局样式（variables.scss, element-ui.scss等）
├── utils/        # 工具函数（request.js, auth.js, aes.js, validate.js等）
├── views/        # 页面组件（home, cases, log, user, project, login, 404）
└── main.js       # 应用入口
```

### 核心模块架构

#### 1. 路由架构（src/router/index.js）
- **数据报表**: `/dashboard` - 首页统计数据展示
- **数据工厂**: `/case/index`, `/case/log`, `/case/detail/:id` - 场景管理
- **管理中心**: `/system/user`, `/system/project`, `/system/project/:id` - 用户和项目管理

#### 2. 状态管理（src/store/）
- **app模块**: 应用状态（sidebar, device, size）
- **user模块**: 用户状态（token, user info, roles）
- **settings模块**: 系统设置（fixedHeader, sidebarLogo, tagsView）
- **tagsView模块**: 标签页管理（visitedViews, cachedViews）
- **Pinia**: 使用Composition API风格的状态管理

#### 3. API层（src/api/）
- **case.js**: 场景相关接口（运行、列表、搜索、详情等）
- **project.js**: 项目管理接口
- **user.js**: 用户管理接口
- **data.js**: 数据相关接口

## 环境配置

### 开发环境（.env.development）
```
VUE_APP_BASE_API = 'http://127.0.0.1:8080/api/'
VUE_APP_AES_KEY = 'SVuRc6B7xsZnUWQO'
VUE_APP_AES_IV = 'MUnDCU0aADgs4hd1'
```

### 生产环境（.env.production）
```
VUE_APP_BASE_API = 'http://10.1.70.12:8080/api/'
VUE_APP_AES_KEY = 'SVuRc6B7xsZnUWQO'
VUE_APP_AES_IV = 'MUnDCU0aADgs4hd1'
```

## 关键功能实现

### 1. 权限控制
- 基于角色的访问控制（RBAC）
- 路由权限校验（src/permission.js）
- 动态路由加载

### 2. 代码编辑器集成
- Monaco Editor集成在 `src/components/Code/monaco.vue`
- 支持多种编程语言语法高亮
- 用于在线编辑和展示测试脚本

### 3. 数据加密
- AES加密实现（src/utils/aes.js）
- 用于敏感数据传输加密

### 4. 图标系统
- SVG图标管理（src/icons/）
- 使用vite-svg-loader处理
- 支持图标自动优化（svgo）

### 5. Vue 3 特性
- Composition API支持
- 更好的TypeScript集成
- 性能优化（更小的包体积和更快的渲染）
- 自动导入API（通过unplugin-auto-import）

## Layout布局系统详解

### 布局结构概览
```
src/layout/
├── index.vue              # 主布局入口
├── components/            # 布局组件
│   ├── index.js          # 组件导出
│   ├── AppMain.vue       # 主内容区域
│   ├── Navbar.vue        # 顶部导航栏
│   ├── Sidebar/          # 侧边栏组件
│   │   ├── index.vue     # 侧边栏主组件
│   │   ├── Logo.vue      # Logo组件
│   │   ├── SidebarItem.vue # 菜单项组件
│   │   ├── Item.vue      # 菜单图标文字组件
│   │   ├── Link.vue      # 菜单链接组件
│   │   └── FixiOSBug.js  # iOS兼容处理
│   └── TagsView/         # 标签页组件
│       ├── index.vue     # 标签页主组件
│       └── ScrollPane.vue # 滚动面板组件
├── composables/          # 组合式函数
└── mixin/               # 混入文件
```

### 核心布局组件

#### 1. 主布局容器 (src/layout/index.vue)
- **功能**: 整体布局框架，包含侧边栏、顶部导航、标签页和主内容区
- **结构**:
  - 侧边栏 (`<sidebar>`)
  - 主容器 (`<div class="main-container">`)
    - 固定头部 (`<div class="fixed-header">`)
      - 导航栏 (`<navbar>`)
    - 标签页 (`<tags-view>`)
    - 主内容 (`<app-main>`)
- **特性**: 支持移动端适配、响应式布局、固定头部

#### 2. 侧边栏组件 (src/layout/components/Sidebar/)
- **主组件** (index.vue): 包含Logo和菜单区域
- **Logo组件** (Logo.vue): 显示系统Logo和标题"微购数据工厂"
- **菜单项组件** (SidebarItem.vue): 递归渲染菜单项，支持多级菜单
- **菜单图标文字** (Item.vue): 显示菜单图标和文字
- **菜单链接** (Link.vue): 处理菜单点击跳转
- **特性**: 支持收起/展开、滚动条、路由激活状态

#### 3. 顶部导航栏 (src/layout/components/Navbar.vue)
- **功能**: 系统顶部导航栏
- **组成**:
  - 汉堡菜单按钮 (控制侧边栏收起/展开)
  - 面包屑导航
  - 右侧用户菜单 (退出登录)
- **特性**: 固定定位、响应式设计

#### 4. 标签页组件 (src/layout/components/TagsView/)
- **主组件** (index.vue): 多标签页管理
- **滚动面板** (ScrollPane.vue): 标签页滚动容器
- **功能**:
  - 显示已访问的页面标签
  - 支持关闭、刷新、关闭其他、关闭全部
  - 右键菜单操作
  - 页面刷新后标签状态保持
- **特性**: 支持拖拽滚动、右键菜单、持久化存储

#### 5. 主内容区域 (src/layout/components/AppMain.vue)
- **功能**: 页面主要内容渲染区域
- **特性**:
  - 包含 `<router-view>` 用于渲染路由组件
  - 支持页面缓存 (`<keep-alive>`)
  - 过渡动画效果
  - 自动计算内容区域高度

### 布局状态管理

#### 1. App Store (src/store/modules/app.js)
```javascript
state: {
  sidebar: {
    opened: true,        // 侧边栏展开状态
    withoutAnimation: false  // 是否禁用动画
  },
  device: 'desktop'     // 设备类型：desktop/mobile
}
```
**核心功能**：
- 侧边栏展开/收起状态管理（持久化到Cookie）
- 设备类型检测和切换
- 移动端自动收起侧边栏

#### 2. Settings Store (src/store/modules/settings.js)
```javascript
state: {
  showSettings: true,    // 显示设置
  fixedHeader: false,    // 固定头部
  tagsView: true,        // 显示标签页
  sidebarLogo: true      // 显示侧边栏Logo
}
```
**核心功能**：系统布局配置项管理

#### 3. TagsView Store (src/store/modules/tagsView.js)
```javascript
state: {
  visitedViews: [],      // 访问过的页面
  cachedViews: []        // 缓存的页面
}
```
**核心功能**：
- 多标签页状态管理
- 页面缓存控制
- 标签页持久化存储（sessionStorage）

### 响应式处理逻辑

#### 1. 响应式钩子 (src/layout/composables/useResizeHandler.js)
- **断点设置**：992px（参考Bootstrap响应式设计）
- **设备检测**：基于`document.body.getBoundingClientRect().width`
- **自动处理**：
  - 窗口大小变化时自动检测设备类型
  - 移动端自动收起侧边栏
  - 路由切换时移动端自动关闭侧边栏

#### 2. 移动端适配策略
- **侧边栏**：滑动菜单形式，遮罩层点击关闭
- **主内容区**：全屏显示，无左侧边距
- **响应式类名**：`mobile`、`hideSidebar`、`withoutAnimation`

### 布局样式系统

#### 1. 样式变量 (src/styles/variables.scss)
```scss
// 侧边栏配色
$menuText: #bfcbd9;           // 菜单文字颜色
$menuActiveText: #409EFF;     // 菜单激活文字颜色
$menuBg: #304156;            // 菜单背景色
$menuHover: #263445;         // 菜单悬停色
$sideBarWidth: 210px;        // 侧边栏宽度
```

#### 2. 布局样式 (src/styles/sidebar.scss)
**核心样式规则**：
- **主容器**：`margin-left: $sideBarWidth` 自适应侧边栏宽度
- **收起状态**：宽度54px，主容器相应调整margin
- **移动端**：侧边栏`transform: translate3d`实现滑动效果
- **过渡动画**：0.28s过渡时间，支持禁用动画

#### 3. 样式特性
- **CSS变量导出**：`:export`语法支持JS/SCSS变量共享
- **Element UI定制**：深度定制菜单组件样式
- **响应式样式**：完整的移动端适配样式
- **滚动条定制**：自定义侧边栏滚动条样式

### 布局架构亮点

#### 1. 组件化设计
- **高内聚**：每个布局组件职责单一
- **低耦合**：组件间通过状态管理通信
- **可复用**：组件可独立使用和维护

#### 2. 状态管理
- **Pinia集成**：Vue 3组合式API风格
- **持久化**：关键状态自动保存到Cookie/sessionStorage
- **响应式**：状态变化自动触发UI更新

#### 3. 用户体验
- **无缝切换**：收起/展开动画流畅
- **智能响应**：自动适配不同设备
- **状态保持**：页面刷新后布局状态不丢失
- **快捷操作**：右键菜单、中键关闭等快捷功能

#### 4. 性能优化
- **按需加载**：组件懒加载
- **缓存机制**：页面缓存减少重复渲染
- **动画优化**：CSS3硬件加速，支持禁用动画

### 使用模式

布局系统采用**中心化配置**模式：
1. **统一入口**：`src/layout/index.vue`作为唯一布局入口
2. **配置驱动**：通过settings.js集中管理布局配置
3. **状态集中**：所有布局状态统一存储在Pinia store
4. **响应式自动**：设备检测和适配完全自动化

这种设计使得布局系统具有**高度可维护性**、**良好扩展性**和**优秀用户体验**，为复杂的后台管理系统提供了坚实的基础架构。

## 开发注意事项

### 1. 代码规范
- 使用ESLint进行代码检查
- 遵循Vue.js 3风格指南
- 组件命名使用PascalCase
- 优先使用Composition API
- 使用TypeScript类型注解（推荐）

### 2. API请求
- 统一通过src/utils/request.js封装
- 自动处理token和错误响应
- 支持请求/响应拦截器

### 3. 样式开发
- 使用SCSS预处理器
- 全局变量定义在src/styles/variables.scss
- Element Plus主题定制在src/styles/element-ui.scss

### 4. 组件开发
- 优先使用Element Plus组件
- 自定义组件放在src/components/
- 保持组件单一职责原则
- 使用Vue 3 Composition API

## 测试策略

### 单元测试
- 使用Vitest测试框架
- 测试文件放在tests/unit/
- 覆盖utils和components模块
- 运行命令：`npm run test:unit`

### 测试覆盖率
- 当前配置覆盖src/utils和src/components
- 排除auth.js和request.js（需要mock）
- 覆盖率报告生成在tests/unit/coverage/

## 构建与部署

### 构建工具
- **Vite**: 快速的构建工具，替代了Vue CLI
- **unplugin-auto-import**: 自动导入Vue 3和Element Plus API
- **unplugin-vue-components**: 自动导入Vue组件
- **Vite SVG Loader**: SVG图标自动处理

### Docker部署
- 提供Dockerfile用于容器化部署
- Nginx配置在nginx.conf
- 支持多环境部署配置

### Vite构建优化
- 基于Rollup的代码分割优化
- 生产环境关闭source map
- 图标和第三方库优化处理
- 快速的模块热重载（HMR）