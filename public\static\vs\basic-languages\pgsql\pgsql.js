/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * monaco-languages version: 1.10.0(1b4729c63bdb0d1e06d4e637e5c3977ddeb714dd)
 * Released under the MIT license
 * https://github.com/Microsoft/monaco-languages/blob/master/LICENSE.md
 *-----------------------------------------------------------------------------*/
define("vs/basic-languages/pgsql/pgsql",["require","exports"],(function(_,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.conf={comments:{lineComment:"--",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}]},e.language={defaultToken:"",tokenPostfix:".sql",ignoreCase:!0,brackets:[{open:"[",close:"]",token:"delimiter.square"},{open:"(",close:")",token:"delimiter.parenthesis"}],keywords:["A","ABORT","ABS","ABSENT","ABSOLUTE","ACCESS","ACCORDING","ACTION","ADA","ADD","ADMIN","AFTER","AGGREGATE","ALL","ALLOCATE","ALSO","ALTER","ALWAYS","ANALYSE","ANALYZE","AND","ANY","ARE","ARRAY","ARRAY_AGG","ARRAY_MAX_CARDINALITY","AS","ASC","ASENSITIVE","ASSERTION","ASSIGNMENT","ASYMMETRIC","AT","ATOMIC","ATTRIBUTE","ATTRIBUTES","AUTHORIZATION","AVG","BACKWARD","BASE64","BEFORE","BEGIN","BEGIN_FRAME","BEGIN_PARTITION","BERNOULLI","BETWEEN","BIGINT","BINARY","BIT","BIT_LENGTH","BLOB","BLOCKED","BOM","BOOLEAN","BOTH","BREADTH","BY","C","CACHE","CALL","CALLED","CARDINALITY","CASCADE","CASCADED","CASE","CAST","CATALOG","CATALOG_NAME","CEIL","CEILING","CHAIN","CHAR","CHARACTER","CHARACTERISTICS","CHARACTERS","CHARACTER_LENGTH","CHARACTER_SET_CATALOG","CHARACTER_SET_NAME","CHARACTER_SET_SCHEMA","CHAR_LENGTH","CHECK","CHECKPOINT","CLASS","CLASS_ORIGIN","CLOB","CLOSE","CLUSTER","COALESCE","COBOL","COLLATE","COLLATION","COLLATION_CATALOG","COLLATION_NAME","COLLATION_SCHEMA","COLLECT","COLUMN","COLUMNS","COLUMN_NAME","COMMAND_FUNCTION","COMMAND_FUNCTION_CODE","COMMENT","COMMENTS","COMMIT","COMMITTED","CONCURRENTLY","CONDITION","CONDITION_NUMBER","CONFIGURATION","CONFLICT","CONNECT","CONNECTION","CONNECTION_NAME","CONSTRAINT","CONSTRAINTS","CONSTRAINT_CATALOG","CONSTRAINT_NAME","CONSTRAINT_SCHEMA","CONSTRUCTOR","CONTAINS","CONTENT","CONTINUE","CONTROL","CONVERSION","CONVERT","COPY","CORR","CORRESPONDING","COST","COUNT","COVAR_POP","COVAR_SAMP","CREATE","CROSS","CSV","CUBE","CUME_DIST","CURRENT","CURRENT_CATALOG","CURRENT_DATE","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROLE","CURRENT_ROW","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CURSOR_NAME","CYCLE","DATA","DATABASE","DATALINK","DATE","DATETIME_INTERVAL_CODE","DATETIME_INTERVAL_PRECISION","DAY","DB","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFAULTS","DEFERRABLE","DEFERRED","DEFINED","DEFINER","DEGREE","DELETE","DELIMITER","DELIMITERS","DENSE_RANK","DEPENDS","DEPTH","DEREF","DERIVED","DESC","DESCRIBE","DESCRIPTOR","DETERMINISTIC","DIAGNOSTICS","DICTIONARY","DISABLE","DISCARD","DISCONNECT","DISPATCH","DISTINCT","DLNEWCOPY","DLPREVIOUSCOPY","DLURLCOMPLETE","DLURLCOMPLETEONLY","DLURLCOMPLETEWRITE","DLURLPATH","DLURLPATHONLY","DLURLPATHWRITE","DLURLSCHEME","DLURLSERVER","DLVALUE","DO","DOCUMENT","DOMAIN","DOUBLE","DROP","DYNAMIC","DYNAMIC_FUNCTION","DYNAMIC_FUNCTION_CODE","EACH","ELEMENT","ELSE","EMPTY","ENABLE","ENCODING","ENCRYPTED","END","END-EXEC","END_FRAME","END_PARTITION","ENFORCED","ENUM","EQUALS","ESCAPE","EVENT","EVERY","EXCEPT","EXCEPTION","EXCLUDE","EXCLUDING","EXCLUSIVE","EXEC","EXECUTE","EXISTS","EXP","EXPLAIN","EXPRESSION","EXTENSION","EXTERNAL","EXTRACT","FALSE","FAMILY","FETCH","FILE","FILTER","FINAL","FIRST","FIRST_VALUE","FLAG","FLOAT","FLOOR","FOLLOWING","FOR","FORCE","FOREIGN","FORTRAN","FORWARD","FOUND","FRAME_ROW","FREE","FREEZE","FROM","FS","FULL","FUNCTION","FUNCTIONS","FUSION","G","GENERAL","GENERATED","GET","GLOBAL","GO","GOTO","GRANT","GRANTED","GREATEST","GROUP","GROUPING","GROUPS","HANDLER","HAVING","HEADER","HEX","HIERARCHY","HOLD","HOUR","ID","IDENTITY","IF","IGNORE","ILIKE","IMMEDIATE","IMMEDIATELY","IMMUTABLE","IMPLEMENTATION","IMPLICIT","IMPORT","IN","INCLUDING","INCREMENT","INDENT","INDEX","INDEXES","INDICATOR","INHERIT","INHERITS","INITIALLY","INLINE","INNER","INOUT","INPUT","INSENSITIVE","INSERT","INSTANCE","INSTANTIABLE","INSTEAD","INT","INTEGER","INTEGRITY","INTERSECT","INTERSECTION","INTERVAL","INTO","INVOKER","IS","ISNULL","ISOLATION","JOIN","K","KEY","KEY_MEMBER","KEY_TYPE","LABEL","LAG","LANGUAGE","LARGE","LAST","LAST_VALUE","LATERAL","LEAD","LEADING","LEAKPROOF","LEAST","LEFT","LENGTH","LEVEL","LIBRARY","LIKE","LIKE_REGEX","LIMIT","LINK","LISTEN","LN","LOAD","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOCATION","LOCATOR","LOCK","LOCKED","LOGGED","LOWER","M","MAP","MAPPING","MATCH","MATCHED","MATERIALIZED","MAX","MAXVALUE","MAX_CARDINALITY","MEMBER","MERGE","MESSAGE_LENGTH","MESSAGE_OCTET_LENGTH","MESSAGE_TEXT","METHOD","MIN","MINUTE","MINVALUE","MOD","MODE","MODIFIES","MODULE","MONTH","MORE","MOVE","MULTISET","MUMPS","NAME","NAMES","NAMESPACE","NATIONAL","NATURAL","NCHAR","NCLOB","NESTING","NEW","NEXT","NFC","NFD","NFKC","NFKD","NIL","NO","NONE","NORMALIZE","NORMALIZED","NOT","NOTHING","NOTIFY","NOTNULL","NOWAIT","NTH_VALUE","NTILE","NULL","NULLABLE","NULLIF","NULLS","NUMBER","NUMERIC","OBJECT","OCCURRENCES_REGEX","OCTETS","OCTET_LENGTH","OF","OFF","OFFSET","OIDS","OLD","ON","ONLY","OPEN","OPERATOR","OPTION","OPTIONS","OR","ORDER","ORDERING","ORDINALITY","OTHERS","OUT","OUTER","OUTPUT","OVER","OVERLAPS","OVERLAY","OVERRIDING","OWNED","OWNER","P","PAD","PARALLEL","PARAMETER","PARAMETER_MODE","PARAMETER_NAME","PARAMETER_ORDINAL_POSITION","PARAMETER_SPECIFIC_CATALOG","PARAMETER_SPECIFIC_NAME","PARAMETER_SPECIFIC_SCHEMA","PARSER","PARTIAL","PARTITION","PASCAL","PASSING","PASSTHROUGH","PASSWORD","PATH","PERCENT","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","PERIOD","PERMISSION","PLACING","PLANS","PLI","POLICY","PORTION","POSITION","POSITION_REGEX","POWER","PRECEDES","PRECEDING","PRECISION","PREPARE","PREPARED","PRESERVE","PRIMARY","PRIOR","PRIVILEGES","PROCEDURAL","PROCEDURE","PROGRAM","PUBLIC","QUOTE","RANGE","RANK","READ","READS","REAL","REASSIGN","RECHECK","RECOVERY","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","REINDEX","RELATIVE","RELEASE","RENAME","REPEATABLE","REPLACE","REPLICA","REQUIRING","RESET","RESPECT","RESTART","RESTORE","RESTRICT","RESULT","RETURN","RETURNED_CARDINALITY","RETURNED_LENGTH","RETURNED_OCTET_LENGTH","RETURNED_SQLSTATE","RETURNING","RETURNS","REVOKE","RIGHT","ROLE","ROLLBACK","ROLLUP","ROUTINE","ROUTINE_CATALOG","ROUTINE_NAME","ROUTINE_SCHEMA","ROW","ROWS","ROW_COUNT","ROW_NUMBER","RULE","SAVEPOINT","SCALE","SCHEMA","SCHEMA_NAME","SCOPE","SCOPE_CATALOG","SCOPE_NAME","SCOPE_SCHEMA","SCROLL","SEARCH","SECOND","SECTION","SECURITY","SELECT","SELECTIVE","SELF","SENSITIVE","SEQUENCE","SEQUENCES","SERIALIZABLE","SERVER","SERVER_NAME","SESSION","SESSION_USER","SET","SETOF","SETS","SHARE","SHOW","SIMILAR","SIMPLE","SIZE","SKIP","SMALLINT","SNAPSHOT","SOME","SOURCE","SPACE","SPECIFIC","SPECIFICTYPE","SPECIFIC_NAME","SQL","SQLCODE","SQLERROR","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQRT","STABLE","STANDALONE","START","STATE","STATEMENT","STATIC","STATISTICS","STDDEV_POP","STDDEV_SAMP","STDIN","STDOUT","STORAGE","STRICT","STRIP","STRUCTURE","STYLE","SUBCLASS_ORIGIN","SUBMULTISET","SUBSTRING","SUBSTRING_REGEX","SUCCEEDS","SUM","SYMMETRIC","SYSID","SYSTEM","SYSTEM_TIME","SYSTEM_USER","T","TABLE","TABLES","TABLESAMPLE","TABLESPACE","TABLE_NAME","TEMP","TEMPLATE","TEMPORARY","TEXT","THEN","TIES","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TOKEN","TOP_LEVEL_COUNT","TRAILING","TRANSACTION","TRANSACTIONS_COMMITTED","TRANSACTIONS_ROLLED_BACK","TRANSACTION_ACTIVE","TRANSFORM","TRANSFORMS","TRANSLATE","TRANSLATE_REGEX","TRANSLATION","TREAT","TRIGGER","TRIGGER_CATALOG","TRIGGER_NAME","TRIGGER_SCHEMA","TRIM","TRIM_ARRAY","TRUE","TRUNCATE","TRUSTED","TYPE","TYPES","UESCAPE","UNBOUNDED","UNCOMMITTED","UNDER","UNENCRYPTED","UNION","UNIQUE","UNKNOWN","UNLINK","UNLISTEN","UNLOGGED","UNNAMED","UNNEST","UNTIL","UNTYPED","UPDATE","UPPER","URI","USAGE","USER","USER_DEFINED_TYPE_CATALOG","USER_DEFINED_TYPE_CODE","USER_DEFINED_TYPE_NAME","USER_DEFINED_TYPE_SCHEMA","USING","VACUUM","VALID","VALIDATE","VALIDATOR","VALUE","VALUES","VALUE_OF","VARBINARY","VARCHAR","VARIADIC","VARYING","VAR_POP","VAR_SAMP","VERBOSE","VERSION","VERSIONING","VIEW","VIEWS","VOLATILE","WHEN","WHENEVER","WHERE","WHITESPACE","WIDTH_BUCKET","WINDOW","WITH","WITHIN","WITHOUT","WORK","WRAPPER","WRITE","XML","XMLAGG","XMLATTRIBUTES","XMLBINARY","XMLCAST","XMLCOMMENT","XMLCONCAT","XMLDECLARATION","XMLDOCUMENT","XMLELEMENT","XMLEXISTS","XMLFOREST","XMLITERATE","XMLNAMESPACES","XMLPARSE","XMLPI","XMLQUERY","XMLROOT","XMLSCHEMA","XMLSERIALIZE","XMLTABLE","XMLTEXT","XMLVALIDATE","YEAR","YES","ZONE"],operators:["AND","BETWEEN","IN","LIKE","NOT","OR","IS","NULL","INTERSECT","UNION","INNER","JOIN","LEFT","OUTER","RIGHT"],builtinFunctions:["abbrev","abs","acos","acosd","age","any","area","array_agg","array_append","array_cat","array_dims","array_fill","array_length","array_lower","array_ndims","array_position","array_positions","array_prepend","array_remove","array_replace","array_to_json","array_to_string","array_to_tsvector","array_upper","ascii","asin","asind","atan","atan2","atan2d","atand","avg","bit","bit_and","bit_length","bit_or","bool_and","bool_or","bound_box","box","brin_summarize_new_values","broadcast","btrim","cardinality","cbrt","ceil","ceiling","center","char_length","character_length","chr","circle","clock_timestamp","coalesce","col_description","concat","concat_ws","convert","convert_from","convert_to","corr","cos","cosd","cot","cotd","count","covar_pop","covar_samp","cume_dist","current_catalog","current_database","current_date","current_query","current_role","current_schema","current_schemas","current_setting","current_time","current_timestamp","current_user","currval","cursor_to_xml","date_part","date_trunc","decode","degrees","dense_rank","diameter","div","encode","enum_first","enum_last","enum_range","every","exp","extract","family","first_value","floor","format","format_type","generate_series","generate_subscripts","get_bit","get_byte","get_current_ts_config","gin_clean_pending_list","greatest","grouping","has_any_column_privilege","has_column_privilege","has_database_privilege","has_foreign_data_wrapper_privilege","has_function_privilege","has_language_privilege","has_schema_privilege","has_sequence_privilege","has_server_privilege","has_table_privilege","has_tablespace_privilege","has_type_privilege","height","host","hostmask","inet_client_addr","inet_client_port","inet_merge","inet_same_family","inet_server_addr","inet_server_port","initcap","isclosed","isempty","isfinite","isopen","json_agg","json_object","json_object_agg","json_populate_record","json_populate_recordset","json_to_record","json_to_recordset","jsonb_agg","jsonb_object_agg","justify_days","justify_hours","justify_interval","lag","last_value","lastval","lead","least","left","length","line","ln","localtime","localtimestamp","log","lower","lower_inc","lower_inf","lpad","lseg","ltrim","make_date","make_interval","make_time","make_timestamp","make_timestamptz","masklen","max","md5","min","mod","mode","netmask","network","nextval","now","npoints","nth_value","ntile","nullif","num_nonnulls","num_nulls","numnode","obj_description","octet_length","overlay","parse_ident","path","pclose","percent_rank","percentile_cont","percentile_disc","pg_advisory_lock","pg_advisory_lock_shared","pg_advisory_unlock","pg_advisory_unlock_all","pg_advisory_unlock_shared","pg_advisory_xact_lock","pg_advisory_xact_lock_shared","pg_backend_pid","pg_backup_start_time","pg_blocking_pids","pg_cancel_backend","pg_client_encoding","pg_collation_is_visible","pg_column_size","pg_conf_load_time","pg_control_checkpoint","pg_control_init","pg_control_recovery","pg_control_system","pg_conversion_is_visible","pg_create_logical_replication_slot","pg_create_physical_replication_slot","pg_create_restore_point","pg_current_xlog_flush_location","pg_current_xlog_insert_location","pg_current_xlog_location","pg_database_size","pg_describe_object","pg_drop_replication_slot","pg_export_snapshot","pg_filenode_relation","pg_function_is_visible","pg_get_constraintdef","pg_get_expr","pg_get_function_arguments","pg_get_function_identity_arguments","pg_get_function_result","pg_get_functiondef","pg_get_indexdef","pg_get_keywords","pg_get_object_address","pg_get_owned_sequence","pg_get_ruledef","pg_get_serial_sequence","pg_get_triggerdef","pg_get_userbyid","pg_get_viewdef","pg_has_role","pg_identify_object","pg_identify_object_as_address","pg_index_column_has_property","pg_index_has_property","pg_indexam_has_property","pg_indexes_size","pg_is_in_backup","pg_is_in_recovery","pg_is_other_temp_schema","pg_is_xlog_replay_paused","pg_last_committed_xact","pg_last_xact_replay_timestamp","pg_last_xlog_receive_location","pg_last_xlog_replay_location","pg_listening_channels","pg_logical_emit_message","pg_logical_slot_get_binary_changes","pg_logical_slot_get_changes","pg_logical_slot_peek_binary_changes","pg_logical_slot_peek_changes","pg_ls_dir","pg_my_temp_schema","pg_notification_queue_usage","pg_opclass_is_visible","pg_operator_is_visible","pg_opfamily_is_visible","pg_options_to_table","pg_postmaster_start_time","pg_read_binary_file","pg_read_file","pg_relation_filenode","pg_relation_filepath","pg_relation_size","pg_reload_conf","pg_replication_origin_create","pg_replication_origin_drop","pg_replication_origin_oid","pg_replication_origin_progress","pg_replication_origin_session_is_setup","pg_replication_origin_session_progress","pg_replication_origin_session_reset","pg_replication_origin_session_setup","pg_replication_origin_xact_reset","pg_replication_origin_xact_setup","pg_rotate_logfile","pg_size_bytes","pg_size_pretty","pg_sleep","pg_sleep_for","pg_sleep_until","pg_start_backup","pg_stat_file","pg_stop_backup","pg_switch_xlog","pg_table_is_visible","pg_table_size","pg_tablespace_databases","pg_tablespace_location","pg_tablespace_size","pg_terminate_backend","pg_total_relation_size","pg_trigger_depth","pg_try_advisory_lock","pg_try_advisory_lock_shared","pg_try_advisory_xact_lock","pg_try_advisory_xact_lock_shared","pg_ts_config_is_visible","pg_ts_dict_is_visible","pg_ts_parser_is_visible","pg_ts_template_is_visible","pg_type_is_visible","pg_typeof","pg_xact_commit_timestamp","pg_xlog_location_diff","pg_xlog_replay_pause","pg_xlog_replay_resume","pg_xlogfile_name","pg_xlogfile_name_offset","phraseto_tsquery","pi","plainto_tsquery","point","polygon","popen","position","power","pqserverversion","query_to_xml","querytree","quote_ident","quote_literal","quote_nullable","radians","radius","random","range_merge","rank","regexp_matches","regexp_replace","regexp_split_to_array","regexp_split_to_table","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","repeat","replace","reverse","right","round","row_number","row_security_active","row_to_json","rpad","rtrim","scale","session_user","set_bit","set_byte","set_config","set_masklen","setseed","setval","setweight","shobj_description","sign","sin","sind","split_part","sprintf","sqrt","statement_timestamp","stddev","stddev_pop","stddev_samp","string_agg","string_to_array","strip","strpos","substr","substring","sum","table_to_xml","table_to_xml_and_xmlschema","tan","tand","text","timeofday","timezone","to_ascii","to_char","to_date","to_hex","to_json","to_number","to_regclass","to_regnamespace","to_regoper","to_regoperator","to_regproc","to_regprocedure","to_regrole","to_regtype","to_timestamp","to_tsquery","to_tsvector","transaction_timestamp","translate","trim","trunc","ts_debug","ts_delete","ts_filter","ts_headline","ts_lexize","ts_parse","ts_rank","ts_rank_cd","ts_rewrite","ts_stat","ts_token_type","tsquery_phrase","tsvector_to_array","tsvector_update_trigger","tsvector_update_trigger_column","txid_current","txid_current_snapshot","txid_snapshot_xip","txid_snapshot_xmax","txid_snapshot_xmin","txid_visible_in_snapshot","unnest","upper","upper_inc","upper_inf","user","var_pop","var_samp","variance","version","width","width_bucket","xml_is_well_formed","xml_is_well_formed_content","xml_is_well_formed_document","xmlagg","xmlcomment","xmlconcat","xmlelement","xmlexists","xmlforest","xmlparse","xmlpi","xmlroot","xmlserialize","xpath","xpath_exists"],builtinVariables:[],pseudoColumns:[],tokenizer:{root:[{include:"@comments"},{include:"@whitespace"},{include:"@pseudoColumns"},{include:"@numbers"},{include:"@strings"},{include:"@complexIdentifiers"},{include:"@scopes"},[/[;,.]/,"delimiter"],[/[()]/,"@brackets"],[/[\w@#$]+/,{cases:{"@keywords":"keyword","@operators":"operator","@builtinVariables":"predefined","@builtinFunctions":"predefined","@default":"identifier"}}],[/[<>=!%&+\-*/|~^]/,"operator"]],whitespace:[[/\s+/,"white"]],comments:[[/--+.*/,"comment"],[/\/\*/,{token:"comment.quote",next:"@comment"}]],comment:[[/[^*/]+/,"comment"],[/\*\//,{token:"comment.quote",next:"@pop"}],[/./,"comment"]],pseudoColumns:[[/[$][A-Za-z_][\w@#$]*/,{cases:{"@pseudoColumns":"predefined","@default":"identifier"}}]],numbers:[[/0[xX][0-9a-fA-F]*/,"number"],[/[$][+-]*\d*(\.\d*)?/,"number"],[/((\d+(\.\d*)?)|(\.\d+))([eE][\-+]?\d+)?/,"number"]],strings:[[/'/,{token:"string",next:"@string"}]],string:[[/[^']+/,"string"],[/''/,"string"],[/'/,{token:"string",next:"@pop"}]],complexIdentifiers:[[/"/,{token:"identifier.quote",next:"@quotedIdentifier"}]],quotedIdentifier:[[/[^"]+/,"identifier"],[/""/,"identifier"],[/"/,{token:"identifier.quote",next:"@pop"}]],scopes:[]}}}));