<template>
  <code-editor
    v-model="text"
    :readonly="readonly"
    :width="width"
    :height="height"
    :lang="lang"
    :theme="theme"
  />
</template>

<script setup>
import { ref, watch } from 'vue'
import CodeEditor from './CodeEditor.vue'

const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  readonly: {
    default: false,
    type: Boolean
  },
  lang: {
    default: 'json',
    type: String
  },
  width: {
    default: '100%',
    type: [String, Number]
  },
  height: {
    default: '400px',
    type: [String, Number]
  }
})

const emit = defineEmits(['codeUpdate'])
const text = ref(props.code)

watch(text, (newVal) => {
  emit('codeUpdate', newVal)
})

watch(() => props.code, (newVal) => {
  text.value = newVal
})

const formatDocument = () => {
  // 简单的JSON格式化
  if (props.lang === 'json') {
    try {
      text.value = JSON.stringify(JSON.parse(text.value), null, 2)
    } catch (e) {
      // 如果不是有效的JSON，保持原样
    }
  }
}

const layout = (code) => {
  if (code) {
    text.value = code
  }
}

// 暴露方法给父组件
defineExpose({
  formatDocument,
  layout
})
</script><style></style>