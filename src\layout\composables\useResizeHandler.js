import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store'

const WIDTH = 992 // refer to Bootstrap's responsive design

export function useResizeHandler() {
  const device = ref('desktop')
  const route = useRoute()
  const appStore = useAppStore()

  const isMobile = () => {
    const rect = document.body.getBoundingClientRect()
    return rect.width - 1 < WIDTH
  }

  const resizeHandler = () => {
    if (!document.hidden) {
      const isMobileDevice = isMobile()
      device.value = isMobileDevice ? 'mobile' : 'desktop'
      appStore.toggleDevice(device.value)

      if (isMobileDevice) {
        appStore.closeSideBar(true)
      }
    }
  }

  watch(() => route.path, () => {
    if (device.value === 'mobile' && appStore.sidebar.opened) {
      appStore.closeSideBar(false)
    }
  })

  onMounted(() => {
    window.addEventListener('resize', resizeHandler)

    const isMobileDevice = isMobile()
    device.value = isMobileDevice ? 'mobile' : 'desktop'
    if (isMobileDevice) {
      appStore.toggleDevice('mobile')
      appStore.closeSideBar(true)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler)
  })

  return {
    device
  }
}