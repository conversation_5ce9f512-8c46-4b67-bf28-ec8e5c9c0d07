Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FEBA
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210285FF9, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB730  0002100690B4 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA10  00021006A49D (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC821B0000 ntdll.dll
7FFC815A0000 KERNEL32.DLL
7FFC7F6A0000 KERNELBASE.dll
7FFC7A300000 apphelp.dll
7FFC81F30000 USER32.dll
7FFC7F670000 win32u.dll
7FFC81AC0000 GDI32.dll
7FFC7F3A0000 gdi32full.dll
7FFC7FD20000 msvcp_win.dll
7FFC7F4D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC81670000 advapi32.dll
7FFC814F0000 msvcrt.dll
7FFC81330000 sechost.dll
7FFC7F2B0000 bcrypt.dll
7FFC81210000 RPCRT4.dll
7FFC7E370000 CRYPTBASE.DLL
7FFC7FDC0000 bcryptPrimitives.dll
7FFC802C0000 IMM32.DLL
