import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import '@/styles/index.scss' // global css

import App from './App.vue'
import router from './router'

import { registerSvgIcon } from '@/icons' // icon
import '@/permission' // permission control
import { copyText } from '@/utils/clipboard' // clipboard utility

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 注册SVG图标
registerSvgIcon(app)

// 全局复制方法
app.config.globalProperties.$copyText = copyText

// 动态设置标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    if (typeof to.meta.title === 'function') {
      document.title = to.meta.title(to.query)
    } else {
      document.title = to.meta.title
    }
  }
  next()
})

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (import.meta.env.PROD) {
  import('../mock').then(({ mockXHR }) => {
    mockXHR()
  })
}

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err, info)
  // 防止错误导致整个应用崩溃
}

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  event.preventDefault()
})

app.mount('#app')
