// 复制文本到剪贴板
export function copyText(text) {
  return new Promise((resolve, reject) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      // 使用现代 Clipboard API
      navigator.clipboard.writeText(text).then(() => {
        resolve()
      }).catch(err => {
        reject(err)
      })
    } else {
      // 降级方案：使用 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        if (successful) {
          resolve()
        } else {
          reject(new Error('Copy failed'))
        }
      } catch (err) {
        document.body.removeChild(textArea)
        reject(err)
      }
    }
  })
}